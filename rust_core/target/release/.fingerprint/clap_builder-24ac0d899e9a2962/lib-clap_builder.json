{"rustc": 11410426090777951712, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 9656904095642909417, "path": 9140383468991027930, "deps": [[5820056977320921005, "anstream", false, 15970700882346337592], [9394696648929125047, "anstyle", false, 2631592263987159138], [11166530783118767604, "strsim", false, 4632923847776207], [11649982696571033535, "clap_lex", false, 5907008396925760961]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_builder-24ac0d899e9a2962/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}