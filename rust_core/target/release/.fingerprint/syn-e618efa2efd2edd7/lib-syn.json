{"rustc": 11410426090777951712, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 1369601567987815722, "path": 16865927889745243262, "deps": [[1988483478007900009, "unicode_ident", false, 8555407956592881497], [3060637413840920116, "proc_macro2", false, 10106953070200039398], [17990358020177143287, "quote", false, 10965982149207348289]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-e618efa2efd2edd7/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}