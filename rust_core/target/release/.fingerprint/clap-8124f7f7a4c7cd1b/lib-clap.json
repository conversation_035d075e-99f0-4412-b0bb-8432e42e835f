{"rustc": 11410426090777951712, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 9656904095642909417, "path": 772634725884498210, "deps": [[1457576002496728321, "clap_derive", false, 8792495700869280135], [7361794428713524931, "clap_builder", false, 1494937912825425761]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap-8124f7f7a4c7cd1b/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}