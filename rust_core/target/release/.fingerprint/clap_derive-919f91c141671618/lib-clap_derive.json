{"rustc": 11410426090777951712, "features": "[\"default\"]", "declared_features": "[\"debug\", \"default\", \"deprecated\", \"raw-deprecated\", \"unstable-markdown\", \"unstable-v5\"]", "target": 905583280159225126, "profile": 12613628788268674035, "path": 12971456178221296730, "deps": [[3060637413840920116, "proc_macro2", false, 10106953070200039398], [4974441333307933176, "syn", false, 9316706820089233571], [13077543566650298139, "heck", false, 13889879401618448241], [17990358020177143287, "quote", false, 10965982149207348289]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_derive-919f91c141671618/dep-lib-clap_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}