{"rustc": 11410426090777951712, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 16865927889745243262, "deps": [[1988483478007900009, "unicode_ident", false, 3222531984055855771], [3060637413840920116, "proc_macro2", false, 8110101496256381478], [17990358020177143287, "quote", false, 8413470835593438489]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-da0068561b49079c/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}