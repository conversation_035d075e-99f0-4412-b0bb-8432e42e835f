{"rustc": 11410426090777951712, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15221872889701672926, "path": 9140383468991027930, "deps": [[5820056977320921005, "anstream", false, 11490029814564781306], [9394696648929125047, "anstyle", false, 15009510379428809890], [11166530783118767604, "strsim", false, 6412528292132751802], [11649982696571033535, "clap_lex", false, 6527458508851945786]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap_builder-35848b1a3c062e85/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}