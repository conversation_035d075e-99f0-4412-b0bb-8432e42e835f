use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use crate::language::Language;
use serde::{Deserialize, Serialize};

/// Represents a function signature found in code
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunctionSignature {
    pub name: String,
    pub parameters: Vec<Parameter>,
    pub return_type: Option<String>,
    pub visibility: Visibility,
    pub is_async: bool,
    pub line_number: usize,
    pub file_path: PathBuf,
    pub docstring: Option<String>,
}

/// Represents a function parameter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Parameter {
    pub name: String,
    pub param_type: Option<String>,
    pub default_value: Option<String>,
}

/// Represents visibility of code elements
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Visibility {
    Public,
    Private,
    Protected,
    Internal,
}

/// Represents a class or struct definition
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ClassDefinition {
    pub name: String,
    pub methods: Vec<FunctionSignature>,
    pub fields: Vec<FieldDefinition>,
    pub inheritance: Vec<String>,
    pub visibility: Visibility,
    pub line_number: usize,
    pub file_path: PathBuf,
    pub docstring: Option<String>,
}

/// Represents a field in a class or struct
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FieldDefinition {
    pub name: String,
    pub field_type: Option<String>,
    pub visibility: Visibility,
    pub is_static: bool,
}

/// Represents an import statement
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportStatement {
    pub module: String,
    pub items: Vec<String>, // Empty for wildcard imports
    pub alias: Option<String>,
    pub is_wildcard: bool,
    pub line_number: usize,
    pub file_path: PathBuf,
}

/// Represents coding patterns found in the codebase
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodingPattern {
    pub pattern_type: PatternType,
    pub description: String,
    pub examples: Vec<String>,
    pub frequency: usize,
}

/// Types of coding patterns
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PatternType {
    NamingConvention,
    ErrorHandling,
    ImportStyle,
    FunctionStructure,
    ClassStructure,
}

/// Complete analysis of a single file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileAnalysis {
    pub file_path: PathBuf,
    pub language: Language,
    pub functions: Vec<FunctionSignature>,
    pub classes: Vec<ClassDefinition>,
    pub imports: Vec<ImportStatement>,
    pub line_count: usize,
    pub complexity_score: f64,
    pub last_analyzed: u64,
}

/// Complete codebase analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodebaseAnalysis {
    pub root_path: PathBuf,
    pub files: HashMap<PathBuf, FileAnalysis>,
    pub global_patterns: Vec<CodingPattern>,
    pub dependency_graph: HashMap<String, Vec<String>>,
    pub total_functions: usize,
    pub total_classes: usize,
    pub total_imports: usize,
    pub last_analyzed: u64,
}

/// Main analyzer struct
pub struct CodebaseAnalyzer {
    pub analysis: Option<CodebaseAnalysis>,
}

impl CodebaseAnalyzer {
    pub fn new() -> Self {
        Self {
            analysis: None,
        }
    }

    /// Analyze the entire codebase starting from the given root path
    pub fn analyze_codebase(&mut self, root_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        println!("🔍 Starting deep codebase analysis...");
        
        let mut files = HashMap::new();
        let mut total_functions = 0;
        let mut total_classes = 0;
        let mut total_imports = 0;

        self.analyze_directory_recursive(root_path, &mut files, &mut total_functions, &mut total_classes, &mut total_imports, 0)?;

        let global_patterns = self.extract_global_patterns(&files);
        let dependency_graph = self.build_dependency_graph(&files);

        self.analysis = Some(CodebaseAnalysis {
            root_path: root_path.to_path_buf(),
            files,
            global_patterns,
            dependency_graph,
            total_functions,
            total_classes,
            total_imports,
            last_analyzed: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        });

        println!("✅ Codebase analysis complete!");
        println!("   📁 Files analyzed: {}", self.analysis.as_ref().unwrap().files.len());
        println!("   🔧 Functions found: {}", total_functions);
        println!("   📦 Classes found: {}", total_classes);
        println!("   📥 Imports found: {}", total_imports);

        Ok(())
    }

    fn analyze_directory_recursive(
        &self,
        path: &Path,
        files: &mut HashMap<PathBuf, FileAnalysis>,
        total_functions: &mut usize,
        total_classes: &mut usize,
        total_imports: &mut usize,
        depth: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if depth > 10 {
            return Ok(());
        }

        // Skip common directories
        if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
            if matches!(dir_name, "target" | "node_modules" | ".git" | "__pycache__" | "build" | "dist" | ".vscode" | ".idea") {
                return Ok(());
            }
        }

        let entries = fs::read_dir(path)?;

        for entry in entries {
            let entry = entry?;
            let entry_path = entry.path();
            let metadata = entry.metadata()?;

            if metadata.is_dir() {
                self.analyze_directory_recursive(&entry_path, files, total_functions, total_classes, total_imports, depth + 1)?;
            } else if self.is_analyzable_file(&entry_path) {
                if let Ok(analysis) = self.analyze_file(&entry_path) {
                    *total_functions += analysis.functions.len();
                    *total_classes += analysis.classes.len();
                    *total_imports += analysis.imports.len();
                    files.insert(entry_path, analysis);
                }
            }
        }

        Ok(())
    }

    fn is_analyzable_file(&self, path: &Path) -> bool {
        if let Some(extension) = path.extension().and_then(|e| e.to_str()) {
            matches!(extension.to_lowercase().as_str(),
                "rs" | "py" | "js" | "jsx" | "ts" | "tsx" | "cpp" | "cc" | "cxx" | "c" |
                "java" | "go" | "rb" | "php" | "cs" | "swift" | "kt" | "scala"
            )
        } else {
            false
        }
    }

    fn analyze_file(&self, path: &Path) -> Result<FileAnalysis, Box<dyn std::error::Error>> {
        let content = fs::read_to_string(path)?;
        let language = self.detect_language_from_path(path);
        
        let functions = self.extract_functions(&content, &language, path)?;
        let classes = self.extract_classes(&content, &language, path)?;
        let imports = self.extract_imports(&content, &language, path)?;
        let line_count = content.lines().count();
        let complexity_score = self.calculate_complexity(&content, &language);

        Ok(FileAnalysis {
            file_path: path.to_path_buf(),
            language,
            functions,
            classes,
            imports,
            line_count,
            complexity_score,
            last_analyzed: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        })
    }

    fn detect_language_from_path(&self, path: &Path) -> Language {
        if let Some(extension) = path.extension().and_then(|e| e.to_str()) {
            match extension.to_lowercase().as_str() {
                "rs" => Language::Rust,
                "py" => Language::Python,
                "js" | "jsx" | "ts" | "tsx" => Language::JavaScript,
                "cpp" | "cc" | "cxx" => Language::Cpp,
                "c" => Language::C,
                "java" => Language::Java,
                "go" => Language::Go,
                "cs" => Language::CSharp,
                _ => Language::Python, // Default fallback
            }
        } else {
            Language::Python
        }
    }

    fn extract_functions(&self, content: &str, language: &Language, path: &Path) -> Result<Vec<FunctionSignature>, Box<dyn std::error::Error>> {
        match language {
            Language::Python => self.extract_python_functions(content, path),
            Language::Rust => self.extract_rust_functions(content, path),
            Language::JavaScript => self.extract_javascript_functions(content, path),
            _ => Ok(Vec::new()), // TODO: Implement other languages
        }
    }

    fn extract_classes(&self, content: &str, language: &Language, path: &Path) -> Result<Vec<ClassDefinition>, Box<dyn std::error::Error>> {
        match language {
            Language::Python => self.extract_python_classes(content, path),
            Language::Rust => self.extract_rust_structs(content, path),
            Language::JavaScript => self.extract_javascript_classes(content, path),
            _ => Ok(Vec::new()), // TODO: Implement other languages
        }
    }

    fn extract_imports(&self, content: &str, language: &Language, path: &Path) -> Result<Vec<ImportStatement>, Box<dyn std::error::Error>> {
        match language {
            Language::Python => self.extract_python_imports(content, path),
            Language::Rust => self.extract_rust_imports(content, path),
            Language::JavaScript => self.extract_javascript_imports(content, path),
            _ => Ok(Vec::new()), // TODO: Implement other languages
        }
    }

    fn calculate_complexity(&self, _content: &str, _language: &Language) -> f64 {
        0.0
    }

    fn extract_global_patterns(&self, _files: &HashMap<PathBuf, FileAnalysis>) -> Vec<CodingPattern> {
        Vec::new()
    }

    fn build_dependency_graph(&self, _files: &HashMap<PathBuf, FileAnalysis>) -> HashMap<String, Vec<String>> {
        HashMap::new()
    }

    /// Get analysis results
    pub fn get_analysis(&self) -> Option<&CodebaseAnalysis> {
        self.analysis.as_ref()
    }

    /// Get functions matching a pattern
    pub fn find_similar_functions(&self, pattern: &str) -> Vec<&FunctionSignature> {
        if let Some(analysis) = &self.analysis {
            analysis.files.values()
                .flat_map(|file| &file.functions)
                .filter(|func| func.name.contains(pattern) || 
                        func.name.to_lowercase().contains(&pattern.to_lowercase()))
                .collect()
        } else {
            Vec::new()
        }
    }

    /// Get import suggestions based on existing patterns
    pub fn suggest_imports(&self, context: &str) -> Vec<String> {
        if let Some(analysis) = &self.analysis {
            let mut suggestions = Vec::new();

            // Find common imports in similar contexts
            for file in analysis.files.values() {
                for import in &file.imports {
                    if import.module.contains(context) || context.contains(&import.module) {
                        suggestions.push(format!("{}", import.module));
                    }
                }
            }

            suggestions.sort();
            suggestions.dedup();
            suggestions
        } else {
            Vec::new()
        }
    }

    // Python-specific parsing methods
    fn extract_python_functions(&self, content: &str, path: &Path) -> Result<Vec<FunctionSignature>, Box<dyn std::error::Error>> {
        let mut functions = Vec::new();
        let lines: Vec<&str> = content.lines().collect();

        for (line_num, line) in lines.iter().enumerate() {
            let trimmed = line.trim();

            // Match function definitions: def function_name(params):
            if trimmed.starts_with("def ") && trimmed.contains('(') && trimmed.ends_with(':') {
                if let Some(func) = self.parse_python_function(trimmed, line_num + 1, path, &lines) {
                    functions.push(func);
                }
            }
        }

        Ok(functions)
    }

    fn parse_python_function(&self, line: &str, line_number: usize, path: &Path, all_lines: &[&str]) -> Option<FunctionSignature> {
        // Extract function name and parameters
        let def_part = line.strip_prefix("def ")?.strip_suffix(':')?;
        let paren_pos = def_part.find('(')?;
        let name = def_part[..paren_pos].trim().to_string();

        let params_str = &def_part[paren_pos + 1..];
        let params_end = params_str.rfind(')')?;
        let params_str = &params_str[..params_end];

        let parameters = self.parse_python_parameters(params_str);

        // Determine visibility (Python convention: leading underscore = private)
        let visibility = if name.starts_with('_') {
            Visibility::Private
        } else {
            Visibility::Public
        };

        // Check for async
        let is_async = line.trim().starts_with("async def");

        // Extract docstring if present
        let docstring = self.extract_python_docstring(line_number, all_lines);

        Some(FunctionSignature {
            name,
            parameters,
            return_type: None, // TODO: Parse type hints
            visibility,
            is_async,
            line_number,
            file_path: path.to_path_buf(),
            docstring,
        })
    }

    fn parse_python_parameters(&self, params_str: &str) -> Vec<Parameter> {
        if params_str.trim().is_empty() {
            return Vec::new();
        }

        params_str.split(',')
            .map(|param| {
                let param = param.trim();
                let (name, param_type, default_value) = if param.contains('=') {
                    let parts: Vec<&str> = param.splitn(2, '=').collect();
                    let name_type = parts[0].trim();
                    let default = Some(parts[1].trim().to_string());

                    if name_type.contains(':') {
                        let type_parts: Vec<&str> = name_type.splitn(2, ':').collect();
                        (type_parts[0].trim().to_string(), Some(type_parts[1].trim().to_string()), default)
                    } else {
                        (name_type.to_string(), None, default)
                    }
                } else if param.contains(':') {
                    let parts: Vec<&str> = param.splitn(2, ':').collect();
                    (parts[0].trim().to_string(), Some(parts[1].trim().to_string()), None)
                } else {
                    (param.to_string(), None, None)
                };

                Parameter {
                    name,
                    param_type,
                    default_value,
                }
            })
            .collect()
    }

    fn extract_python_docstring(&self, start_line: usize, all_lines: &[&str]) -> Option<String> {
        if start_line >= all_lines.len() {
            return None;
        }

        // Look for docstring in the next few lines
        for i in start_line..std::cmp::min(start_line + 5, all_lines.len()) {
            let line = all_lines[i].trim();
            if line.starts_with("\"\"\"") || line.starts_with("'''") {
                let quote = if line.starts_with("\"\"\"") { "\"\"\"" } else { "'''" };

                if line.ends_with(quote) && line.len() > 6 {
                    // Single line docstring
                    return Some(line[3..line.len()-3].trim().to_string());
                } else {
                    // Multi-line docstring
                    let mut docstring = String::new();
                    let mut found_end = false;

                    // Add first line content after opening quotes
                    if line.len() > 3 {
                        docstring.push_str(&line[3..]);
                        docstring.push('\n');
                    }

                    // Look for closing quotes
                    for j in (i + 1)..std::cmp::min(i + 20, all_lines.len()) {
                        let next_line = all_lines[j].trim();
                        if next_line.contains(quote) {
                            let end_pos = next_line.find(quote).unwrap();
                            docstring.push_str(&next_line[..end_pos]);
                            found_end = true;
                            break;
                        } else {
                            docstring.push_str(next_line);
                            docstring.push('\n');
                        }
                    }

                    if found_end {
                        return Some(docstring.trim().to_string());
                    }
                }
                break;
            }
        }

        None
    }

    fn extract_python_classes(&self, content: &str, path: &Path) -> Result<Vec<ClassDefinition>, Box<dyn std::error::Error>> {
        let mut classes = Vec::new();
        let lines: Vec<&str> = content.lines().collect();

        for (line_num, line) in lines.iter().enumerate() {
            let trimmed = line.trim();

            // Match class definitions: class ClassName(BaseClass):
            if trimmed.starts_with("class ") && trimmed.contains(':') {
                if let Some(class) = self.parse_python_class(trimmed, line_num + 1, path, &lines) {
                    classes.push(class);
                }
            }
        }

        Ok(classes)
    }

    fn parse_python_class(&self, line: &str, line_number: usize, path: &Path, all_lines: &[&str]) -> Option<ClassDefinition> {
        let class_part = line.strip_prefix("class ")?.strip_suffix(':')?;

        let (name, inheritance) = if class_part.contains('(') {
            let paren_pos = class_part.find('(')?;
            let name = class_part[..paren_pos].trim().to_string();
            let inheritance_str = &class_part[paren_pos + 1..];
            let inheritance_end = inheritance_str.rfind(')')?;
            let inheritance_str = &inheritance_str[..inheritance_end];

            let inheritance: Vec<String> = inheritance_str
                .split(',')
                .map(|s| s.trim().to_string())
                .filter(|s| !s.is_empty())
                .collect();

            (name, inheritance)
        } else {
            (class_part.trim().to_string(), Vec::new())
        };

        // Determine visibility
        let visibility = if name.starts_with('_') {
            Visibility::Private
        } else {
            Visibility::Public
        };

        // Extract docstring
        let docstring = self.extract_python_docstring(line_number, all_lines);

        // TODO: Extract methods and fields from class body
        let methods = Vec::new();
        let fields = Vec::new();

        Some(ClassDefinition {
            name,
            methods,
            fields,
            inheritance,
            visibility,
            line_number,
            file_path: path.to_path_buf(),
            docstring,
        })
    }

    fn extract_python_imports(&self, content: &str, path: &Path) -> Result<Vec<ImportStatement>, Box<dyn std::error::Error>> {
        let mut imports = Vec::new();
        let lines: Vec<&str> = content.lines().collect();

        for (line_num, line) in lines.iter().enumerate() {
            let trimmed = line.trim();

            // Handle "import module" statements
            if trimmed.starts_with("import ") && !trimmed.starts_with("import ") {
                if let Some(import) = self.parse_python_import(trimmed, line_num + 1, path) {
                    imports.push(import);
                }
            }

            // Handle "from module import item" statements
            if trimmed.starts_with("from ") && trimmed.contains(" import ") {
                if let Some(import) = self.parse_python_from_import(trimmed, line_num + 1, path) {
                    imports.push(import);
                }
            }
        }

        Ok(imports)
    }

    fn parse_python_import(&self, line: &str, line_number: usize, path: &Path) -> Option<ImportStatement> {
        let import_part = line.strip_prefix("import ")?;

        let (module, alias) = if import_part.contains(" as ") {
            let parts: Vec<&str> = import_part.splitn(2, " as ").collect();
            (parts[0].trim().to_string(), Some(parts[1].trim().to_string()))
        } else {
            (import_part.trim().to_string(), None)
        };

        Some(ImportStatement {
            module,
            items: Vec::new(),
            alias,
            is_wildcard: false,
            line_number,
            file_path: path.to_path_buf(),
        })
    }

    fn parse_python_from_import(&self, line: &str, line_number: usize, path: &Path) -> Option<ImportStatement> {
        let from_pos = line.find("from ")?;
        let import_pos = line.find(" import ")?;

        let module = line[from_pos + 5..import_pos].trim().to_string();
        let items_part = line[import_pos + 8..].trim();

        let (items, is_wildcard) = if items_part == "*" {
            (Vec::new(), true)
        } else {
            let items: Vec<String> = items_part
                .split(',')
                .map(|item| {
                    // Handle "item as alias" syntax
                    if item.contains(" as ") {
                        item.splitn(2, " as ").next().unwrap_or(item).trim().to_string()
                    } else {
                        item.trim().to_string()
                    }
                })
                .filter(|s| !s.is_empty())
                .collect();
            (items, false)
        };

        Some(ImportStatement {
            module,
            items,
            alias: None,
            is_wildcard,
            line_number,
            file_path: path.to_path_buf(),
        })
    }

    // Placeholder methods for other languages
    fn extract_rust_functions(&self, _content: &str, _path: &Path) -> Result<Vec<FunctionSignature>, Box<dyn std::error::Error>> {
        Ok(Vec::new()) // TODO: Implement Rust parsing
    }

    fn extract_javascript_functions(&self, _content: &str, _path: &Path) -> Result<Vec<FunctionSignature>, Box<dyn std::error::Error>> {
        Ok(Vec::new()) // TODO: Implement JavaScript parsing
    }

    fn extract_rust_structs(&self, _content: &str, _path: &Path) -> Result<Vec<ClassDefinition>, Box<dyn std::error::Error>> {
        Ok(Vec::new()) // TODO: Implement Rust parsing
    }

    fn extract_javascript_classes(&self, _content: &str, _path: &Path) -> Result<Vec<ClassDefinition>, Box<dyn std::error::Error>> {
        Ok(Vec::new()) // TODO: Implement JavaScript parsing
    }

    fn extract_rust_imports(&self, _content: &str, _path: &Path) -> Result<Vec<ImportStatement>, Box<dyn std::error::Error>> {
        Ok(Vec::new()) // TODO: Implement Rust parsing
    }

    fn extract_javascript_imports(&self, _content: &str, _path: &Path) -> Result<Vec<ImportStatement>, Box<dyn std::error::Error>> {
        Ok(Vec::new()) // TODO: Implement JavaScript parsing
    }
}
