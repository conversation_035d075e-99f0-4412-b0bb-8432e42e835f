use std::collections::HashMap;
use std::io::{self, Write};
use std::fs;
use std::path::{Path, PathBuf};

use crate::command_planner::{CommandPlanner, CommandContext};
use crate::terminal_bridge::{TerminalBridge, ExecutionConfig};
use crate::command_memory::CommandMemory;
use crate::security_layer::SecurityLayer;
use crate::logger::LOGGER;
use crate::config::Config;
use crate::language::Language;
use crate::agent;
use crate::codebase_analyzer::CodebaseAnalyzer;

pub struct InteractiveSession {
    planner: CommandPlanner,
    bridge: TerminalBridge,
    memory: CommandMemory,
    security: SecurityLayer,
    config: Config,
    current_language: Language,
    session_vars: HashMap<String, String>,
    command_aliases: HashMap<String, String>,
    auto_complete_enabled: bool,
    session_id: String,
    // New fields for enhanced code generation
    conversation_context: Vec<ConversationEntry>,
    current_code: Option<GeneratedCode>,
    code_generation_mode: bool,
    // Codebase scanning and analysis
    codebase_info: Option<CodebaseInfo>,
    codebase_analyzer: CodebaseAnalyzer,
}

#[derive(Debug, Clone)]
pub struct ConversationEntry {
    pub user_input: String,
    pub response: String,
    pub timestamp: u64,
    pub code_generated: Option<String>,
}

#[derive(Debug, Clone)]
pub struct GeneratedCode {
    pub content: String,
    pub language: Language,
    pub filename: String,
    pub task_description: String,
    pub last_modified: u64,
}

#[derive(Debug, Clone)]
pub struct CodebaseInfo {
    pub root_path: PathBuf,
    pub total_files: usize,
    pub languages: HashMap<String, usize>,
    pub file_tree: Vec<FileInfo>,
    pub project_type: ProjectType,
    pub last_scanned: u64,
}

#[derive(Debug, Clone)]
pub struct FileInfo {
    pub path: PathBuf,
    pub name: String,
    pub extension: String,
    pub size: u64,
    pub language: Option<Language>,
    pub is_directory: bool,
    pub line_count: Option<usize>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ProjectType {
    Rust,
    Python,
    JavaScript,
    Mixed,
    Unknown,
}

#[derive(Debug, Clone)]
pub struct SessionConfig {
    pub enable_auto_complete: bool,
    pub enable_command_chaining: bool,
    pub enable_history_search: bool,
    pub max_chain_length: usize,
    pub prompt_style: PromptStyle,
}

#[derive(Debug, Clone)]
pub enum PromptStyle {
    Simple,
    Detailed,
    Minimal,
    Custom(String),
}

impl Default for SessionConfig {
    fn default() -> Self {
        Self {
            enable_auto_complete: true,
            enable_command_chaining: true,
            enable_history_search: true,
            max_chain_length: 5,
            prompt_style: PromptStyle::Detailed,
        }
    }
}

impl InteractiveSession {
    pub fn new(config: Config) -> Result<Self, Box<dyn std::error::Error>> {
        let current_language = Language::from_str(&config.default_language)
            .unwrap_or(Language::Python);
        
        let context = CommandContext {
            current_directory: std::env::current_dir()?.to_string_lossy().to_string(),
            available_tools: vec!["python3".to_string(), "git".to_string(), "ls".to_string()],
            recent_commands: Vec::new(),
            project_type: Some("rust".to_string()),
            language_context: Some(current_language.to_string()),
        };
        
        let planner = CommandPlanner::new(context);
        
        let execution_config = ExecutionConfig {
            timeout_seconds: config.default_timeout,
            capture_output: true,
            working_directory: None,
            environment_vars: HashMap::new(),
            max_output_lines: Some(100),
            stream_output: false,
        };
        
        let bridge = TerminalBridge::new(execution_config);
        
        let config_dir = Config::get_config_path()?.parent().unwrap().to_path_buf();
        let data_dir = config_dir.join("data");
        let memory = CommandMemory::new(data_dir.clone())?;

        let security = SecurityLayer::new(config_dir)?;

        let session_id = format!("session_{}",
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs()
        );

        let mut session = Self {
            planner,
            bridge,
            memory,
            security,
            config,
            current_language,
            session_vars: HashMap::new(),
            command_aliases: Self::default_aliases(),
            auto_complete_enabled: true,
            session_id,
            conversation_context: Vec::new(),
            current_code: None,
            code_generation_mode: false,
            codebase_info: None,
            codebase_analyzer: CodebaseAnalyzer::new(),
        };

        // Automatically scan the codebase on startup
        session.auto_scan_codebase();

        Ok(session)
    }

    fn default_aliases() -> HashMap<String, String> {
        let mut aliases = HashMap::new();
        aliases.insert("ll".to_string(), "ls -la".to_string());
        aliases.insert("la".to_string(), "ls -la".to_string());
        aliases.insert("..".to_string(), "cd ..".to_string());
        aliases.insert("cls".to_string(), "clear".to_string());
        aliases.insert("h".to_string(), "history".to_string());
        aliases.insert("q".to_string(), "quit".to_string());
        aliases
    }

    // Enhanced automatic codebase scanning with deep analysis
    fn auto_scan_codebase(&mut self) {
        println!("🔍 Scanning codebase...");

        let current_dir = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));

        // First, do the basic file structure scan
        match self.scan_directory(&current_dir) {
            Ok(codebase_info) => {
                println!("✅ Basic scan complete: {} files, {} languages detected",
                    codebase_info.total_files,
                    codebase_info.languages.len()
                );

                // Show project type and main languages
                println!("📋 Project type: {:?}", codebase_info.project_type);
                if !codebase_info.languages.is_empty() {
                    let mut lang_summary: Vec<_> = codebase_info.languages.iter().collect();
                    lang_summary.sort_by(|a, b| b.1.cmp(a.1));
                    let top_langs: Vec<String> = lang_summary.iter()
                        .take(3)
                        .map(|(lang, count)| format!("{} ({})", lang, count))
                        .collect();
                    println!("🔧 Main languages: {}", top_langs.join(", "));
                }

                // Auto-detect primary language and set it
                if let Some(primary_lang) = self.detect_primary_language(&codebase_info) {
                    self.current_language = primary_lang;
                    println!("🎯 Auto-detected primary language: {}", self.current_language);
                }

                self.codebase_info = Some(codebase_info);

                // Now perform deep content analysis
                println!("🧠 Performing deep code analysis...");
                match self.codebase_analyzer.analyze_codebase(&current_dir) {
                    Ok(()) => {
                        if let Some(analysis) = self.codebase_analyzer.get_analysis() {
                            println!("🎯 Deep analysis complete!");
                            println!("   📊 Functions analyzed: {}", analysis.total_functions);
                            println!("   🏗️  Classes/Structs found: {}", analysis.total_classes);
                            println!("   📦 Import statements: {}", analysis.total_imports);
                            println!("   🔗 Dependency relationships mapped");
                        }
                    }
                    Err(e) => {
                        println!("⚠️ Deep analysis failed: {}", e);
                        println!("   Basic scanning will still work for code generation");
                    }
                }
            }
            Err(e) => {
                println!("⚠️ Failed to scan codebase: {}", e);
            }
        }
        println!();
    }

    fn scan_directory(&self, path: &Path) -> Result<CodebaseInfo, Box<dyn std::error::Error>> {
        let mut file_tree = Vec::new();
        let mut languages = HashMap::new();
        let mut total_files = 0;

        self.scan_directory_recursive(path, &mut file_tree, &mut languages, &mut total_files, 0)?;

        let project_type = self.detect_project_type(&languages, path);

        Ok(CodebaseInfo {
            root_path: path.to_path_buf(),
            total_files,
            languages,
            file_tree,
            project_type,
            last_scanned: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        })
    }

    fn scan_directory_recursive(
        &self,
        path: &Path,
        file_tree: &mut Vec<FileInfo>,
        languages: &mut HashMap<String, usize>,
        total_files: &mut usize,
        depth: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Limit recursion depth to avoid infinite loops
        if depth > 10 {
            return Ok(());
        }

        // Skip common directories that shouldn't be scanned
        if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
            if matches!(dir_name, "target" | "node_modules" | ".git" | "__pycache__" | "build" | "dist" | ".vscode" | ".idea") {
                return Ok(());
            }
        }

        let entries = fs::read_dir(path)?;

        for entry in entries {
            let entry = entry?;
            let entry_path = entry.path();
            let metadata = entry.metadata()?;

            if metadata.is_dir() {
                let dir_info = FileInfo {
                    path: entry_path.clone(),
                    name: entry_path.file_name().unwrap_or_default().to_string_lossy().to_string(),
                    extension: String::new(),
                    size: 0,
                    language: None,
                    is_directory: true,
                    line_count: None,
                };
                file_tree.push(dir_info);

                // Recursively scan subdirectory
                self.scan_directory_recursive(&entry_path, file_tree, languages, total_files, depth + 1)?;
            } else {
                *total_files += 1;

                let file_name = entry_path.file_name().unwrap_or_default().to_string_lossy().to_string();
                let extension = entry_path.extension()
                    .unwrap_or_default()
                    .to_string_lossy()
                    .to_string();

                // Count language files
                if !extension.is_empty() {
                    let lang_name = self.extension_to_language(&extension);
                    *languages.entry(lang_name).or_insert(0) += 1;
                }

                // Get line count for code files
                let line_count = if self.is_code_file(&extension) {
                    self.count_lines(&entry_path).ok()
                } else {
                    None
                };

                let language = self.detect_file_language(&extension);

                let file_info = FileInfo {
                    path: entry_path,
                    name: file_name,
                    extension,
                    size: metadata.len(),
                    language,
                    is_directory: false,
                    line_count,
                };
                file_tree.push(file_info);
            }
        }

        Ok(())
    }

    fn detect_primary_language(&self, codebase_info: &CodebaseInfo) -> Option<Language> {
        // Find the most common language
        let mut max_count = 0;
        let mut primary_lang = None;

        for (lang_name, count) in &codebase_info.languages {
            if *count > max_count {
                max_count = *count;
                primary_lang = match lang_name.as_str() {
                    "rust" => Some(Language::Rust),
                    "python" => Some(Language::Python),
                    "javascript" | "typescript" => Some(Language::JavaScript),
                    "cpp" => Some(Language::Cpp),
                    "c" => Some(Language::C),
                    _ => None,
                };
            }
        }

        primary_lang
    }

    fn detect_project_type(&self, languages: &HashMap<String, usize>, path: &Path) -> ProjectType {
        // Check for specific project files
        if path.join("Cargo.toml").exists() {
            return ProjectType::Rust;
        }
        if path.join("package.json").exists() {
            return ProjectType::JavaScript;
        }
        if path.join("requirements.txt").exists() || path.join("pyproject.toml").exists() || path.join("setup.py").exists() {
            return ProjectType::Python;
        }

        // Check by file count
        let total_files: usize = languages.values().sum();
        if total_files == 0 {
            return ProjectType::Unknown;
        }

        let mut dominant_lang = None;
        let mut max_percentage = 0.0;

        for (lang, count) in languages {
            let percentage = (*count as f64) / (total_files as f64);
            if percentage > max_percentage {
                max_percentage = percentage;
                dominant_lang = Some(lang.as_str());
            }
        }

        match dominant_lang {
            Some("rust") if max_percentage > 0.6 => ProjectType::Rust,
            Some("python") if max_percentage > 0.6 => ProjectType::Python,
            Some("javascript") | Some("typescript") if max_percentage > 0.6 => ProjectType::JavaScript,
            _ => if languages.len() > 2 { ProjectType::Mixed } else { ProjectType::Unknown }
        }
    }

    fn extension_to_language(&self, extension: &str) -> String {
        match extension.to_lowercase().as_str() {
            "rs" => "rust".to_string(),
            "py" => "python".to_string(),
            "js" | "jsx" => "javascript".to_string(),
            "ts" | "tsx" => "typescript".to_string(),
            "cpp" | "cc" | "cxx" => "cpp".to_string(),
            "c" => "c".to_string(),
            "java" => "java".to_string(),
            "go" => "go".to_string(),
            "rb" => "ruby".to_string(),
            "php" => "php".to_string(),
            "cs" => "csharp".to_string(),
            "swift" => "swift".to_string(),
            "kt" => "kotlin".to_string(),
            "scala" => "scala".to_string(),
            "sh" | "bash" => "shell".to_string(),
            "html" => "html".to_string(),
            "css" => "css".to_string(),
            "json" => "json".to_string(),
            "xml" => "xml".to_string(),
            "yaml" | "yml" => "yaml".to_string(),
            "toml" => "toml".to_string(),
            "md" => "markdown".to_string(),
            _ => extension.to_string(),
        }
    }

    fn is_code_file(&self, extension: &str) -> bool {
        matches!(extension.to_lowercase().as_str(),
            "rs" | "py" | "js" | "jsx" | "ts" | "tsx" | "cpp" | "cc" | "cxx" | "c" |
            "java" | "go" | "rb" | "php" | "cs" | "swift" | "kt" | "scala" | "sh" | "bash"
        )
    }

    fn count_lines(&self, path: &Path) -> Result<usize, Box<dyn std::error::Error>> {
        let content = fs::read_to_string(path)?;
        Ok(content.lines().count())
    }

    fn detect_file_language(&self, extension: &str) -> Option<Language> {
        match extension.to_lowercase().as_str() {
            "rs" => Some(Language::Rust),
            "py" => Some(Language::Python),
            "js" | "jsx" | "ts" | "tsx" => Some(Language::JavaScript),
            "cpp" | "cc" | "cxx" => Some(Language::Cpp),
            "c" => Some(Language::C),
            _ => None,
        }
    }

    pub fn run(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        LOGGER.welcome();
        LOGGER.info(&format!("Interactive session started (ID: {})", self.session_id));
        LOGGER.info("Enhanced terminal interaction mode - Type 'help' for commands, 'quit' to exit");
        LOGGER.separator();

        self.show_session_info();

        loop {
            self.display_prompt();
            
            let mut input = String::new();
            match io::stdin().read_line(&mut input) {
                Ok(_) => {
                    let command = input.trim();
                    
                    if command.is_empty() {
                        continue;
                    }
                    
                    // Handle exit commands
                    if matches!(command.to_lowercase().as_str(), "exit" | "quit" | "q") {
                        LOGGER.goodbye();
                        break;
                    }
                    
                    // Process the command
                    if let Err(e) = self.process_command(command) {
                        LOGGER.error(&format!("Command processing error: {}", e));
                    }
                }
                Err(error) => {
                    LOGGER.error(&format!("Error reading input: {}", error));
                    break;
                }
            }
        }
        
        Ok(())
    }

    fn display_prompt(&self) {
        let current_dir = std::env::current_dir()
            .map(|p| p.file_name().unwrap_or_default().to_string_lossy().to_string())
            .unwrap_or_else(|_| "unknown".to_string());
        
        print!("\n🤖 [{}:{}] {} ", 
            self.current_language.to_string().to_lowercase(),
            current_dir,
            ">".bright_blue()
        );
        io::stdout().flush().unwrap();
    }

    fn show_session_info(&self) {
        LOGGER.section("Session Information");
        println!("  Language: {}", self.current_language);
        println!("  Model: {}", self.config.get_effective_model(&self.current_language));
        println!("  Timeout: {}s", self.config.get_effective_timeout(&self.current_language));
        println!("  Testing: {}", if self.config.get_effective_testing(&self.current_language) { "Enabled" } else { "Disabled" });
        println!("  Auto-complete: {}", if self.auto_complete_enabled { "Enabled" } else { "Disabled" });

        // Show codebase information if available
        if let Some(ref codebase_info) = self.codebase_info {
            println!();
            println!("📁 Codebase Context:");
            println!("  Project type: {:?}", codebase_info.project_type);
            println!("  Total files: {}", codebase_info.total_files);
            if !codebase_info.languages.is_empty() {
                let mut lang_summary: Vec<_> = codebase_info.languages.iter().collect();
                lang_summary.sort_by(|a, b| b.1.cmp(a.1));
                let top_langs: Vec<String> = lang_summary.iter()
                    .take(3)
                    .map(|(lang, count)| format!("{} ({})", lang, count))
                    .collect();
                println!("  Languages: {}", top_langs.join(", "));
            }
            println!("  Root path: {}", codebase_info.root_path.display());
        }
    }

    fn process_command(&mut self, input: &str) -> Result<(), Box<dyn std::error::Error>> {
        // Handle command chaining (commands separated by &&, ||, or ;)
        if input.contains("&&") || input.contains("||") || input.contains(";") {
            return self.process_command_chain(input);
        }

        // Expand aliases
        let expanded_input = self.expand_aliases(input);
        
        // Handle built-in commands
        if self.handle_builtin_command(&expanded_input)? {
            return Ok(());
        }

        // Check if this looks like a code generation request
        if self.is_code_generation_request(&expanded_input) {
            return self.handle_code_generation_request(&expanded_input);
        }

        // Handle simple shell commands directly (for quick operations)
        if self.is_simple_shell_command(&expanded_input) {
            let result = self.bridge.execute_interactive_command(&expanded_input);
            if result.success {
                if !result.stdout.is_empty() {
                    println!("{}", result.stdout);
                }
            } else {
                LOGGER.error(&format!("Command failed: {}", result.stderr));
            }
            return Ok(());
        }

        // Show suggestions if enabled
        if self.auto_complete_enabled {
            let suggestions = self.memory.get_suggestions(&expanded_input);
            if !suggestions.is_empty() && suggestions[0] != expanded_input {
                println!("💡 Suggestions: {}", suggestions.join(", "));
            }
        }

        // Plan the command
        let plan = self.planner.plan_command(&expanded_input);
        
        // Show plan summary
        LOGGER.info(&format!("Intent: {} | Risk: {} | Commands: {}", 
            plan.intent, plan.risk_level, plan.commands.len()));

        // Check if confirmation is needed
        if self.memory.should_confirm(&plan) {
            if !self.memory.request_confirmation(&plan) {
                LOGGER.info("Command execution cancelled by user");
                return Ok(());
            }
        }

        // Execute the plan with progress tracking
        let total_commands = plan.commands.len();
        if total_commands > 1 {
            LOGGER.info(&format!("Executing {} commands...", total_commands));
        }

        let results = self.bridge.execute_plan(&plan);

        // Display results
        for (i, result) in results.iter().enumerate() {
            if total_commands > 1 {
                LOGGER.progress_bar(i + 1, total_commands, &format!("Command {}/{}", i + 1, total_commands));
            }
            if result.success {
                if !result.stdout.is_empty() {
                    LOGGER.output_header();
                    println!("{}", result.stdout);
                }
            } else {
                LOGGER.error_header();
                if !result.stderr.is_empty() {
                    println!("{}", result.stderr);
                }
            }
        }

        // Add to memory
        self.memory.add_execution(expanded_input, plan, results);
        
        Ok(())
    }

    fn process_command_chain(&mut self, input: &str) -> Result<(), Box<dyn std::error::Error>> {
        LOGGER.info("Processing command chain");
        
        // Simple chain parsing (can be enhanced)
        let commands: Vec<&str> = if input.contains("&&") {
            input.split("&&").collect()
        } else if input.contains("||") {
            input.split("||").collect()
        } else {
            input.split(";").collect()
        };

        for (i, cmd) in commands.iter().enumerate() {
            let cmd = cmd.trim();
            if cmd.is_empty() {
                continue;
            }
            
            LOGGER.info(&format!("Executing command {}/{}: {}", i + 1, commands.len(), cmd));
            
            if let Err(e) = self.process_command(cmd) {
                LOGGER.error(&format!("Command chain failed at step {}: {}", i + 1, e));
                break;
            }
        }
        
        Ok(())
    }

    fn expand_aliases(&self, input: &str) -> String {
        let parts: Vec<&str> = input.split_whitespace().collect();
        if parts.is_empty() {
            return input.to_string();
        }

        if let Some(alias_expansion) = self.command_aliases.get(parts[0]) {
            if parts.len() > 1 {
                format!("{} {}", alias_expansion, parts[1..].join(" "))
            } else {
                alias_expansion.clone()
            }
        } else {
            input.to_string()
        }
    }

    fn handle_builtin_command(&mut self, input: &str) -> Result<bool, Box<dyn std::error::Error>> {
        let parts: Vec<&str> = input.split_whitespace().collect();
        if parts.is_empty() {
            return Ok(false);
        }

        match parts[0] {
            "help" => {
                self.show_enhanced_help();
                Ok(true)
            }
            "history" => {
                let limit = if parts.len() > 1 {
                    parts[1].parse().ok()
                } else {
                    None
                };
                self.memory.show_history(limit);
                Ok(true)
            }
            "alias" => {
                if parts.len() == 3 {
                    self.command_aliases.insert(parts[1].to_string(), parts[2].to_string());
                    LOGGER.success(&format!("Alias created: {} -> {}", parts[1], parts[2]));
                } else {
                    println!("Current aliases:");
                    for (alias, command) in &self.command_aliases {
                        println!("  {} -> {}", alias, command);
                    }
                }
                Ok(true)
            }
            "set" => {
                if parts.len() == 3 {
                    self.session_vars.insert(parts[1].to_string(), parts[2].to_string());
                    LOGGER.success(&format!("Variable set: {}={}", parts[1], parts[2]));
                } else {
                    println!("Current session variables:");
                    for (key, value) in &self.session_vars {
                        println!("  {}={}", key, value);
                    }
                }
                Ok(true)
            }
            "autocomplete" => {
                if parts.len() > 1 {
                    match parts[1] {
                        "on" | "enable" => {
                            self.auto_complete_enabled = true;
                            LOGGER.success("Auto-complete enabled");
                        }
                        "off" | "disable" => {
                            self.auto_complete_enabled = false;
                            LOGGER.success("Auto-complete disabled");
                        }
                        _ => {
                            LOGGER.error("Usage: autocomplete [on|off]");
                        }
                    }
                } else {
                    println!("Auto-complete is {}", if self.auto_complete_enabled { "enabled" } else { "disabled" });
                }
                Ok(true)
            }
            "status" => {
                self.show_session_info();
                Ok(true)
            }
            "clear" => {
                print!("\x1B[2J\x1B[1;1H"); // ANSI clear screen
                Ok(true)
            }
            "security" => {
                self.handle_security_command(&parts[1..])?;
                Ok(true)
            }
            "ps" => {
                let processes = self.bridge.list_active_processes();
                if processes.is_empty() {
                    LOGGER.info("No active processes");
                } else {
                    println!("Active processes: {:?}", processes);
                }
                Ok(true)
            }
            "kill" => {
                if parts.len() > 1 {
                    if let Ok(pid) = parts[1].parse::<u32>() {
                        match self.bridge.kill_process(pid) {
                            Ok(_) => LOGGER.success(&format!("Process {} terminated", pid)),
                            Err(e) => LOGGER.error(&format!("Failed to kill process {}: {}", pid, e)),
                        }
                    } else {
                        LOGGER.error("Invalid process ID");
                    }
                } else {
                    LOGGER.error("Usage: kill <pid>");
                }
                Ok(true)
            }
            "timeout" => {
                if parts.len() > 1 {
                    if let Ok(timeout) = parts[1].parse::<u64>() {
                        let new_config = crate::terminal_bridge::ExecutionConfig {
                            timeout_seconds: timeout,
                            capture_output: true,
                            working_directory: None,
                            environment_vars: std::collections::HashMap::new(),
                            max_output_lines: Some(100),
                            stream_output: false,
                        };
                        self.bridge.update_config(new_config);
                        LOGGER.success(&format!("Execution timeout updated to {}s", timeout));
                    } else {
                        LOGGER.error("Invalid timeout value");
                    }
                } else {
                    LOGGER.error("Usage: timeout <seconds>");
                }
                Ok(true)
            }
            "analyze" => {
                self.handle_analyze_command(&parts[1..])?;
                Ok(true)
            }
            "functions" => {
                self.handle_functions_command(&parts[1..])?;
                Ok(true)
            }
            "imports" => {
                self.handle_imports_command(&parts[1..])?;
                Ok(true)
            }
            "suggest" => {
                self.handle_suggest_command(&parts[1..])?;
                Ok(true)
            }
            "rescan" => {
                self.auto_scan_codebase();
                Ok(true)
            }
            _ => Ok(false)
        }
    }

    fn handle_security_command(&mut self, args: &[&str]) -> Result<(), Box<dyn std::error::Error>> {
        if args.is_empty() {
            self.security.show_policy();
            return Ok(());
        }

        match args[0] {
            "allow" => {
                if args.len() > 1 {
                    self.security.add_allowed_command(args[1].to_string())?;
                } else {
                    LOGGER.error("Usage: security allow <command>");
                }
            }
            "block" => {
                if args.len() > 1 {
                    self.security.add_blocked_command(args[1].to_string())?;
                } else {
                    LOGGER.error("Usage: security block <command>");
                }
            }
            "unallow" => {
                if args.len() > 1 {
                    self.security.remove_allowed_command(args[1])?;
                } else {
                    LOGGER.error("Usage: security unallow <command>");
                }
            }
            "unblock" => {
                if args.len() > 1 {
                    self.security.remove_blocked_command(args[1])?;
                } else {
                    LOGGER.error("Usage: security unblock <command>");
                }
            }
            "audit" => {
                let limit = if args.len() > 1 {
                    args[1].parse().ok()
                } else {
                    None
                };
                self.security.show_audit_log(limit);
            }
            "dry-run" => {
                if args.len() > 1 {
                    match args[1] {
                        "on" | "enable" => {
                            self.security.enable_dry_run();
                        }
                        "off" | "disable" => {
                            self.security.disable_dry_run();
                        }
                        _ => {
                            LOGGER.error("Usage: security dry-run [on|off]");
                        }
                    }
                } else {
                    let status = if self.security.is_dry_run_enabled() { "enabled" } else { "disabled" };
                    LOGGER.info(&format!("Dry-run mode is {}", status));
                }
            }
            "sandbox" => {
                self.security.create_sandbox_environment()?;
            }
            "help" => {
                self.show_security_help();
            }
            _ => {
                LOGGER.error(&format!("Unknown security command: {}", args[0]));
                self.show_security_help();
            }
        }

        Ok(())
    }

    fn is_simple_shell_command(&self, input: &str) -> bool {
        let simple_commands = [
            "ls", "pwd", "whoami", "date", "echo", "cat", "head", "tail",
            "grep", "find", "which", "uname", "df", "du", "free", "uptime"
        ];

        let first_word = input.split_whitespace().next().unwrap_or("");
        simple_commands.contains(&first_word)
    }

    fn show_security_help(&self) {
        LOGGER.section("Security Commands");
        println!("🔒 Available security commands:");
        println!("  • security - Show current security policy");
        println!("  • security allow <cmd> - Add command to allowlist");
        println!("  • security block <cmd> - Add command to blocklist");
        println!("  • security unallow <cmd> - Remove command from allowlist");
        println!("  • security unblock <cmd> - Remove command from blocklist");
        println!("  • security audit [n] - Show security audit log (last n entries)");
        println!("  • security dry-run [on|off] - Enable/disable dry-run mode");
        println!("  • security sandbox - Create sandbox environment");
        println!("  • security help - Show this help");
    }

    fn show_enhanced_help(&self) {
        LOGGER.section("Enhanced Interactive Commands");
        
        println!("🎯 Code Generation:");
        println!("  • Just type your request: 'create a fibonacci function'");
        println!("  • Chain commands: 'create function && run tests'");
        println!();
        
        println!("🔧 Built-in Commands:");
        println!("  • help - Show this help");
        println!("  • history [n] - Show command history (last n entries)");
        println!("  • status - Show current session status");
        println!("  • alias [name] [command] - Create or list aliases");
        println!("  • set [var] [value] - Set or list session variables");
        println!("  • autocomplete [on|off] - Toggle auto-completion");
        println!("  • clear - Clear screen");
        println!("  • security - Security management commands");
        println!("  • ps - List active processes");
        println!("  • kill <pid> - Terminate a process");
        println!("  • timeout <seconds> - Update execution timeout");
        println!();
        
        println!("⚡ Command Chaining:");
        println!("  • cmd1 && cmd2 - Run cmd2 only if cmd1 succeeds");
        println!("  • cmd1 || cmd2 - Run cmd2 only if cmd1 fails");
        println!("  • cmd1 ; cmd2 - Run both commands sequentially");
        println!();
        
        println!("🎨 Current Aliases:");
        for (alias, command) in &self.command_aliases {
            println!("  • {} -> {}", alias, command);
        }

        println!();
        println!("🤖 Code Generation Commands:");
        println!("  • generate <task> - Generate code for a specific task");
        println!("  • refine <changes> - Refine the current generated code");
        println!("  • explain - Explain the current generated code");
        println!("  • test - Test the current generated code");
        println!("  • save [filename] - Save the current code to a file");
        println!("  • show - Show the current generated code");
        println!("  • lang <language> - Change the programming language");
        println!();
        println!("📁 Enhanced Codebase Analysis Commands:");
        println!("  • rescan - Re-scan and analyze the current codebase");
        println!("  • analyze - Show overall codebase analysis summary");
        println!("  • analyze file <filename> - Analyze a specific file in detail");
        println!("  • analyze patterns - Show detected coding patterns");
        println!("  • analyze dependencies - Show dependency relationships");
        println!("  • functions - Show functions summary");
        println!("  • functions search <pattern> - Search for functions by name");
        println!("  • imports - Show import analysis and statistics");
        println!("  • imports suggest <context> - Get import suggestions");
        println!("  • suggest functions <pattern> - Find similar functions");
        println!("  • suggest imports <context> - Get import suggestions");
        println!("  • suggest naming <text> - Get naming convention suggestions");
        println!();
        println!("📊 Legacy Commands (still available):");
        println!("  • scan - Basic codebase scan");
        println!("  • tree - Show the project file tree");
        println!("  • files [extension] - List files by extension");
        println!("  • stats - Show codebase statistics");
        println!("  • find <pattern> - Find files matching pattern");
    }

    // Enhanced code generation methods
    fn is_code_generation_request(&self, input: &str) -> bool {
        // First check if it's a built-in command - if so, it's NOT a code generation request
        let parts: Vec<&str> = input.split_whitespace().collect();
        if !parts.is_empty() {
            let builtin_commands = [
                "help", "history", "alias", "set", "autocomplete", "status", "clear",
                "security", "ps", "kill", "timeout", "analyze", "functions", "imports",
                "suggest", "rescan", "scan", "tree", "files", "stats", "find"
            ];

            if builtin_commands.contains(&parts[0]) {
                return false;
            }
        }

        let code_keywords = [
            "generate", "create", "write", "build", "make", "implement",
            "function", "class", "script", "program", "code", "algorithm",
            "refine", "improve", "modify", "change", "update", "fix"
        ];

        let input_lower = input.to_lowercase();
        code_keywords.iter().any(|&keyword| input_lower.contains(keyword)) ||
        input.starts_with("generate ") ||
        input.starts_with("refine ") ||
        (input.starts_with("explain") && !input.starts_with("explain ")) || // Only bare "explain"
        (input.starts_with("test") && input.len() > 4 && !input.starts_with("test ")) || // Only "test something"
        (input.starts_with("show") && input.len() > 4 && !input.starts_with("show ")) || // Only "show something"
        input.starts_with("save ")
    }

    fn handle_code_generation_request(&mut self, input: &str) -> Result<(), Box<dyn std::error::Error>> {
        let input_lower = input.to_lowercase();

        if input_lower.starts_with("generate ") {
            let task = &input[9..]; // Remove "generate "
            self.generate_code(task)
        } else if input_lower.starts_with("refine ") {
            let changes = &input[7..]; // Remove "refine "
            self.refine_current_code(changes)
        } else if input_lower == "explain" {
            self.explain_current_code()
        } else if input_lower == "test" {
            self.test_current_code()
        } else if input_lower == "show" {
            self.show_current_code()
        } else if input_lower.starts_with("save") {
            let filename = if input.len() > 5 { Some(input[5..].trim()) } else { None };
            self.save_current_code(filename)
        } else if input_lower.starts_with("lang ") {
            let language = &input[5..];
            self.change_language(language)
        } else {
            // Treat as a general code generation request
            self.generate_code(input)
        }
    }

    fn generate_code(&mut self, task: &str) -> Result<(), Box<dyn std::error::Error>> {
        LOGGER.info(&format!("🤖 Generating code for: {}", task));

        // Add to conversation context
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        // Generate code using the existing agent with codebase context
        let model = self.config.get_effective_model(&self.current_language);
        let base_constraints = self.current_language.get_prompt_context();

        // Add codebase context to constraints
        let constraints = if let Some(ref codebase_info) = self.codebase_info {
            let context_info = format!(
                "\n\nCodebase Context:\n- Project type: {:?}\n- Total files: {}\n- Main languages: {}\n- Working directory: {}",
                codebase_info.project_type,
                codebase_info.total_files,
                codebase_info.languages.keys().cloned().collect::<Vec<_>>().join(", "),
                codebase_info.root_path.display()
            );
            format!("{}{}", base_constraints, context_info)
        } else {
            base_constraints.to_string()
        };

        LOGGER.info("🔄 Calling code generation...");
        agent::generate_code_via_python(task, &model, &self.current_language, &constraints);

        // Try to read the generated code
        let filename = self.current_language.output_filename();
        match fs::read_to_string(&filename) {
            Ok(code_content) => {
                LOGGER.success("✅ Code generated successfully!");

                // Store the generated code
                self.current_code = Some(GeneratedCode {
                    content: code_content.clone(),
                    language: self.current_language.clone(),
                    filename: filename.clone(),
                    task_description: task.to_string(),
                    last_modified: timestamp,
                });

                // Add to conversation context
                self.conversation_context.push(ConversationEntry {
                    user_input: task.to_string(),
                    response: "Code generated successfully".to_string(),
                    timestamp,
                    code_generated: Some(code_content.clone()),
                });

                // Show the generated code
                LOGGER.section("Generated Code");
                println!("{}", code_content);
                LOGGER.separator();

                LOGGER.info("💡 You can now use: 'test', 'refine <changes>', 'explain', or 'save [filename]'");
            }
            Err(e) => {
                LOGGER.error(&format!("Failed to read generated code: {}", e));
                return Err(Box::new(e));
            }
        }

        Ok(())
    }

    fn refine_current_code(&mut self, changes: &str) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(ref current) = self.current_code.clone() {
            LOGGER.info(&format!("🔧 Refining code with: {}", changes));

            // Create a refinement prompt
            let refinement_task = format!(
                "Refine this {} code:\n\n```{}\n{}\n```\n\nChanges requested: {}",
                self.current_language.to_string().to_lowercase(),
                self.current_language.file_extension(),
                current.content,
                changes
            );

            self.generate_code(&refinement_task)
        } else {
            LOGGER.error("No code to refine. Generate code first with 'generate <task>'");
            Ok(())
        }
    }

    fn explain_current_code(&self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(ref current) = self.current_code {
            LOGGER.section("Code Explanation");
            println!("📝 Task: {}", current.task_description);
            println!("🔧 Language: {}", current.language);
            println!("📁 File: {}", current.filename);
            println!("🕒 Generated: {}", self.format_timestamp(current.last_modified));
            println!();
            println!("📋 Code:");
            println!("{}", current.content);
            LOGGER.separator();
        } else {
            LOGGER.error("No code to explain. Generate code first with 'generate <task>'");
        }
        Ok(())
    }

    fn test_current_code(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(ref current) = self.current_code {
            LOGGER.info("🧪 Testing generated code...");

            // Use the existing runner to test the code
            let config = crate::runner::ExecutionConfig {
                timeout_seconds: self.config.default_timeout,
                max_memory_mb: Some(512),
            };

            crate::runner::run_generated_code_with_config(&current.language, &config);
        } else {
            LOGGER.error("No code to test. Generate code first with 'generate <task>'");
        }
        Ok(())
    }

    fn show_current_code(&self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(ref current) = self.current_code {
            LOGGER.section(&format!("Current {} Code", current.language));
            println!("{}", current.content);
            LOGGER.separator();
        } else {
            LOGGER.info("No code generated yet. Use 'generate <task>' to create code.");
        }
        Ok(())
    }

    fn save_current_code(&self, filename: Option<&str>) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(ref current) = self.current_code {
            let save_filename = filename.unwrap_or(&current.filename);

            fs::write(save_filename, &current.content)?;
            LOGGER.success(&format!("💾 Code saved to: {}", save_filename));
        } else {
            LOGGER.error("No code to save. Generate code first with 'generate <task>'");
        }
        Ok(())
    }

    fn change_language(&mut self, language_str: &str) -> Result<(), Box<dyn std::error::Error>> {
        match Language::from_str(language_str) {
            Ok(new_language) => {
                self.current_language = new_language.clone();
                LOGGER.success(&format!("🔧 Language changed to: {}", new_language));
            }
            Err(e) => {
                LOGGER.error(&format!("Invalid language: {}", e));
                LOGGER.info("Available languages: Python, Rust, JavaScript, TypeScript, Go, Java, C#, C++, C");
            }
        }
        Ok(())
    }

    fn format_timestamp(&self, timestamp: u64) -> String {
        // Simple timestamp formatting - could be enhanced
        format!("{} seconds ago",
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs() - timestamp
        )
    }

    // Enhanced codebase analysis command handlers
    fn handle_analyze_command(&mut self, args: &[&str]) -> Result<(), Box<dyn std::error::Error>> {
        if args.is_empty() {
            // Show overall analysis summary
            if let Some(analysis) = self.codebase_analyzer.get_analysis() {
                println!("📊 Codebase Analysis Summary");
                println!("═══════════════════════════");
                println!("📁 Root path: {}", analysis.root_path.display());
                println!("📄 Files analyzed: {}", analysis.files.len());
                println!("🔧 Total functions: {}", analysis.total_functions);
                println!("🏗️  Total classes/structs: {}", analysis.total_classes);
                println!("📦 Total imports: {}", analysis.total_imports);
                println!("🔗 Dependencies mapped: {}", analysis.dependency_graph.len());
                println!("🕒 Last analyzed: {}", self.format_timestamp(analysis.last_analyzed));

                if !analysis.global_patterns.is_empty() {
                    println!("\n🎯 Detected Patterns:");
                    for pattern in &analysis.global_patterns {
                        println!("  • {}: {} ({}x)",
                            format!("{:?}", pattern.pattern_type),
                            pattern.description,
                            pattern.frequency
                        );
                    }
                }
            } else {
                println!("❌ No analysis data available. Run 'rescan' to analyze the codebase.");
            }
        } else {
            match args[0] {
                "file" => {
                    if args.len() > 1 {
                        self.analyze_specific_file(args[1])?;
                    } else {
                        println!("❌ Usage: analyze file <filename>");
                    }
                }
                "patterns" => {
                    self.show_coding_patterns()?;
                }
                "dependencies" => {
                    self.show_dependency_graph()?;
                }
                _ => {
                    println!("❌ Unknown analyze command. Available: file, patterns, dependencies");
                }
            }
        }
        Ok(())
    }

    fn handle_functions_command(&mut self, args: &[&str]) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(analysis) = self.codebase_analyzer.get_analysis() {
            if args.is_empty() {
                // Show all functions summary
                println!("🔧 Functions Summary");
                println!("═══════════════════");

                let mut function_count_by_file = std::collections::HashMap::new();
                for file_analysis in analysis.files.values() {
                    function_count_by_file.insert(
                        file_analysis.file_path.file_name()
                            .unwrap_or_default()
                            .to_string_lossy()
                            .to_string(),
                        file_analysis.functions.len()
                    );
                }

                let mut sorted_files: Vec<_> = function_count_by_file.iter().collect();
                sorted_files.sort_by(|a, b| b.1.cmp(a.1));

                for (file, count) in sorted_files.iter().take(10) {
                    println!("  📄 {}: {} functions", file, count);
                }

                if sorted_files.len() > 10 {
                    println!("  ... and {} more files", sorted_files.len() - 10);
                }
            } else {
                match args[0] {
                    "search" => {
                        if args.len() > 1 {
                            let pattern = args[1];
                            let matches = self.codebase_analyzer.find_similar_functions(pattern);

                            println!("🔍 Functions matching '{}':", pattern);
                            println!("═══════════════════════════");

                            for func in matches.iter().take(20) {
                                println!("  🔧 {} ({}:{})",
                                    func.name,
                                    func.file_path.file_name().unwrap_or_default().to_string_lossy(),
                                    func.line_number
                                );
                                if let Some(ref docstring) = func.docstring {
                                    let short_doc = if docstring.len() > 60 {
                                        format!("{}...", &docstring[..60])
                                    } else {
                                        docstring.clone()
                                    };
                                    println!("     💬 {}", short_doc);
                                }
                            }

                            if matches.len() > 20 {
                                println!("  ... and {} more matches", matches.len() - 20);
                            }
                        } else {
                            println!("❌ Usage: functions search <pattern>");
                        }
                    }
                    _ => {
                        println!("❌ Unknown functions command. Available: search");
                    }
                }
            }
        } else {
            println!("❌ No analysis data available. Run 'rescan' to analyze the codebase.");
        }
        Ok(())
    }

    fn handle_imports_command(&mut self, args: &[&str]) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(analysis) = self.codebase_analyzer.get_analysis() {
            if args.is_empty() {
                // Show import statistics
                println!("📦 Import Analysis");
                println!("═════════════════");

                let mut module_counts = std::collections::HashMap::new();
                for file_analysis in analysis.files.values() {
                    for import in &file_analysis.imports {
                        *module_counts.entry(import.module.clone()).or_insert(0) += 1;
                    }
                }

                let mut sorted_modules: Vec<_> = module_counts.iter().collect();
                sorted_modules.sort_by(|a, b| b.1.cmp(a.1));

                println!("Most frequently imported modules:");
                for (module, count) in sorted_modules.iter().take(15) {
                    println!("  📦 {}: {} times", module, count);
                }
            } else {
                match args[0] {
                    "suggest" => {
                        if args.len() > 1 {
                            let context = args[1];
                            let suggestions = self.codebase_analyzer.suggest_imports(context);

                            println!("💡 Import suggestions for '{}':", context);
                            println!("═══════════════════════════════");

                            for suggestion in suggestions.iter().take(10) {
                                println!("  📦 {}", suggestion);
                            }

                            if suggestions.is_empty() {
                                println!("  No suggestions found for this context.");
                            }
                        } else {
                            println!("❌ Usage: imports suggest <context>");
                        }
                    }
                    _ => {
                        println!("❌ Unknown imports command. Available: suggest");
                    }
                }
            }
        } else {
            println!("❌ No analysis data available. Run 'rescan' to analyze the codebase.");
        }
        Ok(())
    }

    fn handle_suggest_command(&mut self, args: &[&str]) -> Result<(), Box<dyn std::error::Error>> {
        if args.is_empty() {
            println!("💡 Available suggestions:");
            println!("  suggest functions <pattern> - Find similar functions");
            println!("  suggest imports <context>   - Get import suggestions");
            println!("  suggest naming <text>       - Get naming convention suggestions");
            return Ok(());
        }

        match args[0] {
            "functions" => {
                if args.len() > 1 {
                    let pattern = args[1];
                    let matches = self.codebase_analyzer.find_similar_functions(pattern);

                    println!("💡 Similar functions to '{}':", pattern);
                    for func in matches.iter().take(5) {
                        println!("  🔧 {}", func.name);
                    }
                } else {
                    println!("❌ Usage: suggest functions <pattern>");
                }
            }
            "imports" => {
                if args.len() > 1 {
                    let context = args[1];
                    let suggestions = self.codebase_analyzer.suggest_imports(context);

                    println!("💡 Import suggestions for '{}':", context);
                    for suggestion in suggestions.iter().take(5) {
                        println!("  📦 {}", suggestion);
                    }
                } else {
                    println!("❌ Usage: suggest imports <context>");
                }
            }
            "naming" => {
                println!("💡 Naming convention suggestions based on codebase patterns:");
                println!("  (This feature will be implemented in the next enhancement)");
            }
            _ => {
                println!("❌ Unknown suggestion type. Available: functions, imports, naming");
            }
        }
        Ok(())
    }

    // Helper methods for enhanced analysis commands
    fn analyze_specific_file(&self, filename: &str) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(analysis) = self.codebase_analyzer.get_analysis() {
            // Find the file in the analysis
            let matching_files: Vec<_> = analysis.files.iter()
                .filter(|(path, _)| {
                    path.file_name()
                        .unwrap_or_default()
                        .to_string_lossy()
                        .contains(filename)
                })
                .collect();

            if matching_files.is_empty() {
                println!("❌ File '{}' not found in analysis", filename);
                return Ok(());
            }

            for (path, file_analysis) in matching_files.iter().take(1) {
                println!("📄 File Analysis: {}", path.display());
                println!("═══════════════════════════════");
                println!("🔧 Language: {:?}", file_analysis.language);
                println!("📏 Lines: {}", file_analysis.line_count);
                println!("🔧 Functions: {}", file_analysis.functions.len());
                println!("🏗️  Classes: {}", file_analysis.classes.len());
                println!("📦 Imports: {}", file_analysis.imports.len());
                println!("📊 Complexity: {:.2}", file_analysis.complexity_score);

                if !file_analysis.functions.is_empty() {
                    println!("\n🔧 Functions:");
                    for func in &file_analysis.functions {
                        println!("  • {} (line {})", func.name, func.line_number);
                        if !func.parameters.is_empty() {
                            let params: Vec<String> = func.parameters.iter()
                                .map(|p| p.name.clone())
                                .collect();
                            println!("    Parameters: {}", params.join(", "));
                        }
                    }
                }

                if !file_analysis.classes.is_empty() {
                    println!("\n🏗️  Classes/Structs:");
                    for class in &file_analysis.classes {
                        println!("  • {} (line {})", class.name, class.line_number);
                        if !class.inheritance.is_empty() {
                            println!("    Inherits: {}", class.inheritance.join(", "));
                        }
                    }
                }

                if !file_analysis.imports.is_empty() {
                    println!("\n📦 Imports:");
                    for import in &file_analysis.imports {
                        if import.is_wildcard {
                            println!("  • from {} import *", import.module);
                        } else if import.items.is_empty() {
                            println!("  • import {}", import.module);
                        } else {
                            println!("  • from {} import {}", import.module, import.items.join(", "));
                        }
                    }
                }
            }
        } else {
            println!("❌ No analysis data available. Run 'rescan' to analyze the codebase.");
        }
        Ok(())
    }

    fn show_coding_patterns(&self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(analysis) = self.codebase_analyzer.get_analysis() {
            println!("🎯 Coding Patterns Analysis");
            println!("═══════════════════════════");

            if analysis.global_patterns.is_empty() {
                println!("No specific patterns detected yet.");
                println!("Pattern detection will be enhanced in future updates.");
            } else {
                for pattern in &analysis.global_patterns {
                    println!("📋 {:?}", pattern.pattern_type);
                    println!("   Description: {}", pattern.description);
                    println!("   Frequency: {} occurrences", pattern.frequency);

                    if !pattern.examples.is_empty() {
                        println!("   Examples:");
                        for example in pattern.examples.iter().take(3) {
                            println!("     • {}", example);
                        }
                    }
                    println!();
                }
            }
        } else {
            println!("❌ No analysis data available. Run 'rescan' to analyze the codebase.");
        }
        Ok(())
    }

    fn show_dependency_graph(&self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(analysis) = self.codebase_analyzer.get_analysis() {
            println!("🔗 Dependency Graph");
            println!("══════════════════");

            if analysis.dependency_graph.is_empty() {
                println!("No dependency relationships mapped yet.");
                println!("Dependency analysis will be enhanced in future updates.");
            } else {
                for (module, dependencies) in &analysis.dependency_graph {
                    println!("📦 {}", module);
                    for dep in dependencies {
                        println!("  └─ depends on: {}", dep);
                    }
                    println!();
                }
            }
        } else {
            println!("❌ No analysis data available. Run 'rescan' to analyze the codebase.");
        }
        Ok(())
    }
}

use colored::*; // Add this import for the bright_blue() method
