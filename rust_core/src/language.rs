use std::fmt;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum Language {
    Python,
    Rust,
    JavaScript,
    TypeScript,
    Go,
    Java,
    CSharp,
    Cpp,
    C,
}

impl Language {
    pub fn from_str(s: &str) -> Result<Self, String> {
        match s.to_lowercase().as_str() {
            "python" | "py" => Ok(Language::Python),
            "rust" | "rs" => Ok(Language::Rust),
            "javascript" | "js" => Ok(Language::JavaScript),
            "typescript" | "ts" => Ok(Language::TypeScript),
            "go" | "golang" => Ok(Language::Go),
            "java" => Ok(Language::Java),
            "csharp" | "c#" | "cs" => Ok(Language::CSharp),
            "cpp" | "c++" => Ok(Language::Cpp),
            "c" => Ok(Language::C),
            _ => Err(format!("Unsupported language: {}", s)),
        }
    }

    pub fn file_extension(&self) -> &'static str {
        match self {
            Language::Python => "py",
            Language::Rust => "rs",
            Language::JavaScript => "js",
            Language::TypeScript => "ts",
            Language::Go => "go",
            Language::Java => "java",
            Language::CSharp => "cs",
            Language::Cpp => "cpp",
            Language::C => "c",
        }
    }

    pub fn output_filename(&self) -> String {
        format!("../shared/generated_code.{}", self.file_extension())
    }

    pub fn run_command(&self) -> Vec<String> {
        match self {
            Language::Python => vec!["python3".to_string()],
            Language::Rust => vec!["rustc".to_string(), "--edition=2021".to_string()],
            Language::JavaScript => vec!["node".to_string()],
            Language::TypeScript => vec!["npx".to_string(), "ts-node".to_string()],
            Language::Go => vec!["go".to_string(), "run".to_string()],
            Language::Java => vec!["java".to_string()],
            Language::CSharp => vec!["dotnet".to_string(), "run".to_string()],
            Language::Cpp => vec!["g++".to_string(), "-o".to_string(), "temp_executable".to_string()],
            Language::C => vec!["gcc".to_string(), "-o".to_string(), "temp_executable".to_string()],
        }
    }

    pub fn compile_command(&self, filename: &str) -> Option<Vec<String>> {
        match self {
            Language::Rust => Some(vec![
                "rustc".to_string(),
                "--edition=2021".to_string(),
                "-o".to_string(),
                "temp_executable".to_string(),
                filename.to_string(),
            ]),
            Language::Java => Some(vec![
                "javac".to_string(),
                filename.to_string(),
            ]),
            Language::Cpp => Some(vec![
                "g++".to_string(),
                "-o".to_string(),
                "temp_executable".to_string(),
                filename.to_string(),
            ]),
            Language::C => Some(vec![
                "gcc".to_string(),
                "-o".to_string(),
                "temp_executable".to_string(),
                filename.to_string(),
            ]),
            _ => None, // Interpreted languages don't need compilation
        }
    }

    pub fn execute_command(&self, filename: &str) -> Vec<String> {
        match self {
            Language::Python => vec!["python3".to_string(), filename.to_string()],
            Language::JavaScript => vec!["node".to_string(), filename.to_string()],
            Language::TypeScript => vec!["npx".to_string(), "ts-node".to_string(), filename.to_string()],
            Language::Go => vec!["go".to_string(), "run".to_string(), filename.to_string()],
            Language::Java => {
                let class_name = filename.replace(".java", "");
                vec!["java".to_string(), class_name]
            },
            Language::Rust | Language::Cpp | Language::C => vec!["./temp_executable".to_string()],
            Language::CSharp => vec!["dotnet".to_string(), "run".to_string()],
        }
    }

    pub fn get_prompt_context(&self) -> &'static str {
        match self {
            Language::Python => "Write clean, readable Python code with proper error handling and comments.",
            Language::Rust => "Write safe, efficient Rust code following Rust best practices and idioms.",
            Language::JavaScript => "Write modern JavaScript code (ES6+) with proper error handling.",
            Language::TypeScript => "Write TypeScript code with proper type annotations and interfaces.",
            Language::Go => "Write idiomatic Go code with proper error handling and formatting.",
            Language::Java => "Write clean Java code following Java conventions and best practices.",
            Language::CSharp => "Write C# code following .NET conventions and best practices.",
            Language::Cpp => "Write modern C++ code (C++17 or later) with proper memory management.",
            Language::C => "Write clean C code with proper memory management and error handling.",
        }
    }
}

impl fmt::Display for Language {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        let name = match self {
            Language::Python => "Python",
            Language::Rust => "Rust",
            Language::JavaScript => "JavaScript",
            Language::TypeScript => "TypeScript",
            Language::Go => "Go",
            Language::Java => "Java",
            Language::CSharp => "C#",
            Language::Cpp => "C++",
            Language::C => "C",
        };
        write!(f, "{}", name)
    }
}

pub fn list_supported_languages() -> Vec<Language> {
    vec![
        Language::Python,
        Language::Rust,
        Language::JavaScript,
        Language::TypeScript,
        Language::Go,
        Language::Java,
        Language::CSharp,
        Language::Cpp,
        Language::C,
    ]
}
