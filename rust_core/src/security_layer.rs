use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use std::fs;
use std::path::PathBuf;
use crate::command_planner::{CommandPlan, RiskLevel, PlannedCommand};
use crate::logger::LOGGER;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SecurityPolicy {
    pub allowed_commands: HashSet<String>,
    pub blocked_commands: HashSet<String>,
    pub allowed_directories: HashSet<String>,
    pub blocked_directories: HashSet<String>,
    pub max_execution_time: u64,
    pub max_memory_usage: Option<u64>,
    pub enable_sandboxing: bool,
    pub dry_run_mode: bool,
    pub require_confirmation_for: HashSet<RiskLevel>,
    pub auto_block_dangerous: bool,
}

impl Default for SecurityPolicy {
    fn default() -> Self {
        let mut allowed_commands = HashSet::new();
        allowed_commands.insert("ls".to_string());
        allowed_commands.insert("cat".to_string());
        allowed_commands.insert("echo".to_string());
        allowed_commands.insert("pwd".to_string());
        allowed_commands.insert("whoami".to_string());
        allowed_commands.insert("date".to_string());
        allowed_commands.insert("python3".to_string());
        allowed_commands.insert("python".to_string());
        allowed_commands.insert("node".to_string());
        allowed_commands.insert("git".to_string());
        allowed_commands.insert("cargo".to_string());
        allowed_commands.insert("rustc".to_string());

        let mut blocked_commands = HashSet::new();
        blocked_commands.insert("rm".to_string());
        blocked_commands.insert("rmdir".to_string());
        blocked_commands.insert("del".to_string());
        blocked_commands.insert("format".to_string());
        blocked_commands.insert("fdisk".to_string());
        blocked_commands.insert("mkfs".to_string());
        blocked_commands.insert("dd".to_string());
        blocked_commands.insert("shutdown".to_string());
        blocked_commands.insert("reboot".to_string());
        blocked_commands.insert("halt".to_string());
        blocked_commands.insert("init".to_string());

        let mut require_confirmation_for = HashSet::new();
        require_confirmation_for.insert(RiskLevel::High);
        require_confirmation_for.insert(RiskLevel::Critical);

        Self {
            allowed_commands,
            blocked_commands,
            allowed_directories: HashSet::new(), // Empty means all allowed
            blocked_directories: HashSet::new(),
            max_execution_time: 300, // 5 minutes
            max_memory_usage: Some(1024), // 1GB
            enable_sandboxing: false,
            dry_run_mode: false,
            require_confirmation_for,
            auto_block_dangerous: true,
        }
    }
}

#[derive(Debug, Clone)]
pub enum SecurityViolation {
    BlockedCommand(String),
    BlockedDirectory(String),
    ExcessiveTimeout(u64, u64), // requested, max_allowed
    DangerousOperation(String),
    UnauthorizedAccess(String),
    PolicyViolation(String),
}

#[derive(Debug, Clone)]
pub struct SecurityCheck {
    pub allowed: bool,
    pub violations: Vec<SecurityViolation>,
    pub warnings: Vec<String>,
    pub modified_plan: Option<CommandPlan>,
}

pub struct SecurityLayer {
    policy: SecurityPolicy,
    policy_file: PathBuf,
    sandbox_enabled: bool,
    audit_log: Vec<SecurityAuditEntry>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAuditEntry {
    pub timestamp: u64,
    pub command: String,
    pub risk_level: RiskLevel,
    pub allowed: bool,
    pub violations: Vec<String>,
    pub user_override: bool,
}

impl SecurityLayer {
    pub fn new(config_dir: PathBuf) -> Result<Self, Box<dyn std::error::Error>> {
        let policy_file = config_dir.join("security_policy.json");
        
        let policy = if policy_file.exists() {
            let content = fs::read_to_string(&policy_file)?;
            serde_json::from_str(&content).unwrap_or_default()
        } else {
            SecurityPolicy::default()
        };

        let layer = Self {
            policy,
            policy_file,
            sandbox_enabled: false,
            audit_log: Vec::new(),
        };

        layer.save_policy()?;
        Ok(layer)
    }

    pub fn check_command_plan(&mut self, plan: &CommandPlan) -> SecurityCheck {
        let mut violations = Vec::new();
        let mut warnings = Vec::new();
        let mut allowed = true;

        // Check each command in the plan
        for command in &plan.commands {
            let command_check = self.check_single_command(command);
            
            if !command_check.allowed {
                allowed = false;
                violations.extend(command_check.violations);
            }
            warnings.extend(command_check.warnings);
        }

        // Check overall risk level
        if self.policy.require_confirmation_for.contains(&plan.risk_level) {
            warnings.push(format!("Command requires confirmation due to {} risk level", plan.risk_level));
        }

        // Auto-block dangerous operations
        if self.policy.auto_block_dangerous && plan.risk_level == RiskLevel::Critical {
            allowed = false;
            violations.push(SecurityViolation::DangerousOperation(
                "Critical risk operations are automatically blocked".to_string()
            ));
        }

        // Log the security check
        self.audit_log.push(SecurityAuditEntry {
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            command: format!("{:?}", plan.commands),
            risk_level: plan.risk_level.clone(),
            allowed,
            violations: violations.iter().map(|v| format!("{:?}", v)).collect(),
            user_override: false,
        });

        SecurityCheck {
            allowed,
            violations,
            warnings,
            modified_plan: None,
        }
    }

    fn check_single_command(&self, command: &PlannedCommand) -> SecurityCheck {
        let mut violations = Vec::new();
        let mut warnings = Vec::new();
        let mut allowed = true;

        // Check if command is explicitly blocked
        if self.policy.blocked_commands.contains(&command.command) {
            allowed = false;
            violations.push(SecurityViolation::BlockedCommand(command.command.clone()));
        }

        // Check if command is in allowed list (if allowlist is not empty)
        if !self.policy.allowed_commands.is_empty() && 
           !self.policy.allowed_commands.contains(&command.command) {
            allowed = false;
            violations.push(SecurityViolation::BlockedCommand(
                format!("{} (not in allowlist)", command.command)
            ));
        }

        // Check working directory restrictions
        if let Some(ref wd) = command.working_directory {
            if self.is_directory_blocked(wd) {
                allowed = false;
                violations.push(SecurityViolation::BlockedDirectory(wd.clone()));
            }
        }

        // Check timeout limits
        if let Some(timeout) = command.timeout_seconds {
            if timeout > self.policy.max_execution_time {
                allowed = false;
                violations.push(SecurityViolation::ExcessiveTimeout(
                    timeout, 
                    self.policy.max_execution_time
                ));
            }
        }

        // Check for dangerous patterns in arguments
        for arg in &command.args {
            if self.contains_dangerous_pattern(arg) {
                warnings.push(format!("Potentially dangerous argument: {}", arg));
            }
        }

        SecurityCheck {
            allowed,
            violations,
            warnings,
            modified_plan: None,
        }
    }

    fn is_directory_blocked(&self, directory: &str) -> bool {
        // Check blocked directories
        for blocked_dir in &self.policy.blocked_directories {
            if directory.starts_with(blocked_dir) {
                return true;
            }
        }

        // Check allowed directories (if specified)
        if !self.policy.allowed_directories.is_empty() {
            for allowed_dir in &self.policy.allowed_directories {
                if directory.starts_with(allowed_dir) {
                    return false;
                }
            }
            return true; // Not in allowed list
        }

        false
    }

    fn contains_dangerous_pattern(&self, arg: &str) -> bool {
        let dangerous_patterns = [
            "rm -rf",
            "sudo",
            "chmod 777",
            "chown",
            "passwd",
            "su -",
            ">/dev/",
            "format",
            "fdisk",
            "mkfs",
            "dd if=",
            ":(){ :|:& };:", // Fork bomb
        ];

        let arg_lower = arg.to_lowercase();
        dangerous_patterns.iter().any(|&pattern| arg_lower.contains(pattern))
    }

    pub fn enable_dry_run(&mut self) {
        self.policy.dry_run_mode = true;
        LOGGER.info("Dry-run mode enabled - commands will be simulated only");
    }

    pub fn disable_dry_run(&mut self) {
        self.policy.dry_run_mode = false;
        LOGGER.info("Dry-run mode disabled - commands will be executed");
    }

    pub fn is_dry_run_enabled(&self) -> bool {
        self.policy.dry_run_mode
    }

    pub fn add_allowed_command(&mut self, command: String) -> Result<(), Box<dyn std::error::Error>> {
        self.policy.allowed_commands.insert(command.clone());
        self.save_policy()?;
        LOGGER.success(&format!("Added '{}' to allowed commands", command));
        Ok(())
    }

    pub fn add_blocked_command(&mut self, command: String) -> Result<(), Box<dyn std::error::Error>> {
        self.policy.blocked_commands.insert(command.clone());
        self.save_policy()?;
        LOGGER.success(&format!("Added '{}' to blocked commands", command));
        Ok(())
    }

    pub fn remove_allowed_command(&mut self, command: &str) -> Result<(), Box<dyn std::error::Error>> {
        if self.policy.allowed_commands.remove(command) {
            self.save_policy()?;
            LOGGER.success(&format!("Removed '{}' from allowed commands", command));
        } else {
            LOGGER.warning(&format!("Command '{}' was not in allowed list", command));
        }
        Ok(())
    }

    pub fn remove_blocked_command(&mut self, command: &str) -> Result<(), Box<dyn std::error::Error>> {
        if self.policy.blocked_commands.remove(command) {
            self.save_policy()?;
            LOGGER.success(&format!("Removed '{}' from blocked commands", command));
        } else {
            LOGGER.warning(&format!("Command '{}' was not in blocked list", command));
        }
        Ok(())
    }

    pub fn show_policy(&self) {
        LOGGER.section("Security Policy");
        
        println!("🔒 General Settings:");
        println!("  • Dry-run mode: {}", if self.policy.dry_run_mode { "Enabled" } else { "Disabled" });
        println!("  • Sandboxing: {}", if self.policy.enable_sandboxing { "Enabled" } else { "Disabled" });
        println!("  • Auto-block dangerous: {}", if self.policy.auto_block_dangerous { "Yes" } else { "No" });
        println!("  • Max execution time: {}s", self.policy.max_execution_time);
        if let Some(mem) = self.policy.max_memory_usage {
            println!("  • Max memory usage: {}MB", mem);
        }

        if !self.policy.allowed_commands.is_empty() {
            println!("\n✅ Allowed Commands:");
            for cmd in &self.policy.allowed_commands {
                println!("  • {}", cmd);
            }
        }

        if !self.policy.blocked_commands.is_empty() {
            println!("\n❌ Blocked Commands:");
            for cmd in &self.policy.blocked_commands {
                println!("  • {}", cmd);
            }
        }

        if !self.policy.require_confirmation_for.is_empty() {
            println!("\n⚠️  Require Confirmation For:");
            for risk in &self.policy.require_confirmation_for {
                println!("  • {} risk commands", risk);
            }
        }
    }

    pub fn show_audit_log(&self, limit: Option<usize>) {
        let limit = limit.unwrap_or(10);
        let recent_entries: Vec<_> = self.audit_log.iter().rev().take(limit).collect();
        
        if recent_entries.is_empty() {
            LOGGER.info("No security audit entries available");
            return;
        }
        
        LOGGER.section("Security Audit Log");
        
        for entry in recent_entries {
            let status = if entry.allowed { "✅" } else { "❌" };
            let time_ago = (std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs() - entry.timestamp) as f64 / 60.0;
            
            println!("{} {} ({:.1}m ago) - {} risk", 
                status, 
                entry.command,
                time_ago,
                entry.risk_level
            );
            
            if !entry.violations.is_empty() {
                for violation in &entry.violations {
                    println!("    ⚠️  {}", violation);
                }
            }
        }
    }

    fn save_policy(&self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(parent) = self.policy_file.parent() {
            fs::create_dir_all(parent)?;
        }
        
        let content = serde_json::to_string_pretty(&self.policy)?;
        fs::write(&self.policy_file, content)?;
        Ok(())
    }

    pub fn create_sandbox_environment(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // This would implement actual sandboxing using containers, chroot, or similar
        // For now, we'll just enable the flag and log
        self.sandbox_enabled = true;
        self.policy.enable_sandboxing = true;
        LOGGER.success("Sandbox environment created (simulated)");
        Ok(())
    }

    pub fn user_override_security(&mut self, command: &str) -> bool {
        LOGGER.warning(&format!("Security override requested for: {}", command));
        
        print!("⚠️  This command was blocked by security policy. Override? [y/N]: ");
        std::io::Write::flush(&mut std::io::stdout()).unwrap();
        
        let mut input = String::new();
        match std::io::stdin().read_line(&mut input) {
            Ok(_) => {
                let response = input.trim().to_lowercase();
                let override_granted = response == "y" || response == "yes";
                
                if override_granted {
                    LOGGER.warning("Security override granted by user");
                    // Update the last audit entry to reflect the override
                    if let Some(last_entry) = self.audit_log.last_mut() {
                        last_entry.user_override = true;
                    }
                } else {
                    LOGGER.info("Security override denied by user");
                }
                
                override_granted
            }
            Err(_) => {
                LOGGER.error("Failed to read user input, denying override");
                false
            }
        }
    }
}

impl std::fmt::Display for SecurityViolation {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SecurityViolation::BlockedCommand(cmd) => write!(f, "Blocked command: {}", cmd),
            SecurityViolation::BlockedDirectory(dir) => write!(f, "Blocked directory access: {}", dir),
            SecurityViolation::ExcessiveTimeout(req, max) => write!(f, "Timeout too long: {}s requested, {}s maximum", req, max),
            SecurityViolation::DangerousOperation(desc) => write!(f, "Dangerous operation: {}", desc),
            SecurityViolation::UnauthorizedAccess(resource) => write!(f, "Unauthorized access: {}", resource),
            SecurityViolation::PolicyViolation(desc) => write!(f, "Policy violation: {}", desc),
        }
    }
}
