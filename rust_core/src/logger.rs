use colored::*;
use std::fmt;

#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>)]
pub enum LogLevel {
    Info,
    Success,
    Warning,
    Error,
    Debug,
}

impl fmt::Display for LogLevel {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            LogLevel::Info => write!(f, "{}", "INFO".blue()),
            LogLevel::Success => write!(f, "{}", "SUCCESS".green()),
            LogLevel::Warning => write!(f, "{}", "WARNING".yellow()),
            LogLevel::Error => write!(f, "{}", "ERROR".red()),
            LogLevel::Debug => write!(f, "{}", "DEBUG".purple()),
        }
    }
}

pub struct Logger {
    pub show_debug: bool,
}

impl Logger {
    pub fn new(show_debug: bool) -> Self {
        Self { show_debug }
    }

    pub fn log(&self, level: LogLevel, message: &str) {
        if matches!(level, LogLevel::Debug) && !self.show_debug {
            return;
        }

        let prefix = match level {
            LogLevel::Info => "ℹ️",
            LogLevel::Success => "✅",
            LogLevel::Warning => "⚠️",
            LogLevel::Error => "❌",
            LogLevel::Debug => "🐛",
        };

        println!("{} [{}] {}", prefix, level, message);
    }

    pub fn info(&self, message: &str) {
        self.log(LogLevel::Info, message);
    }

    pub fn success(&self, message: &str) {
        self.log(LogLevel::Success, message);
    }

    pub fn warning(&self, message: &str) {
        self.log(LogLevel::Warning, message);
    }

    pub fn error(&self, message: &str) {
        self.log(LogLevel::Error, message);
    }

    pub fn debug(&self, message: &str) {
        self.log(LogLevel::Debug, message);
    }

    // Specialized logging methods for different contexts
    pub fn task_start(&self, task: &str) {
        println!("{}", "=".repeat(60).bright_blue());
        println!("{} {}", "🤖 Starting Task:".bright_cyan().bold(), task.white().bold());
        println!("{}", "=".repeat(60).bright_blue());
    }

    pub fn task_complete(&self, task: &str) {
        println!("{}", "=".repeat(60).bright_green());
        println!("{} {}", "✅ Task Completed:".bright_green().bold(), task.white().bold());
        println!("{}", "=".repeat(60).bright_green());
    }

    pub fn section(&self, title: &str) {
        println!("\n{} {}", "📋".bright_blue(), title.bright_blue().bold());
        println!("{}", "-".repeat(40).blue());
    }

    pub fn code_generation(&self) {
        println!("{} {}", "🔄".bright_yellow(), "Generating code...".bright_yellow());
    }

    pub fn compilation(&self) {
        println!("{} {}", "🔨".bright_magenta(), "Compiling...".bright_magenta());
    }

    pub fn execution(&self, timeout: u64) {
        println!("{} {} ({}s timeout)", "⚡".bright_green(), "Executing code...".bright_green(), timeout.to_string().yellow());
    }

    pub fn testing(&self, count: usize) {
        println!("{} {} ({} tests)", "🧪".bright_cyan(), "Running tests...".bright_cyan(), count.to_string().yellow());
    }

    pub fn output_header(&self) {
        println!("\n{} {}", "📤".bright_green(), "Output:".bright_green().bold());
        println!("{}", "-".repeat(30).green());
    }

    pub fn error_header(&self) {
        println!("\n{} {}", "🚨".bright_red(), "Error:".bright_red().bold());
        println!("{}", "-".repeat(30).red());
    }

    pub fn settings(&self, language: &str, model: &str, timeout: u64, testing: bool) {
        println!("\n{} {}", "⚙️".bright_blue(), "Current Settings:".bright_blue().bold());
        println!("  {} {}", "Language:".cyan(), language.white().bold());
        println!("  {} {}", "Model:".cyan(), model.white());
        println!("  {} {}s", "Timeout:".cyan(), timeout.to_string().white());
        println!("  {} {}", "Testing:".cyan(), 
            if testing { "Enabled".green().bold() } else { "Disabled".red() });
    }

    pub fn interactive_prompt(&self, language: &str) {
        print!("{} [{}] {} ", "🤖".bright_blue(), language.bright_green().bold(), ">".bright_blue());
    }

    pub fn welcome(&self) {
        println!("{}", "🚀 Welcome to Alim Agent!".bright_cyan().bold());
        println!("{}", "Your AI-powered code generation assistant".bright_blue());
    }

    pub fn goodbye(&self) {
        println!("{} {}", "👋".bright_yellow(), "Goodbye! Happy coding!".bright_yellow().bold());
    }

    pub fn progress_bar(&self, current: usize, total: usize, message: &str) {
        let percentage = (current as f32 / total as f32 * 100.0) as usize;
        let filled = percentage / 5; // 20 chars max
        let empty = 20 - filled;
        
        let bar = format!("{}{}",
            "█".repeat(filled).bright_green(),
            "░".repeat(empty).bright_black()
        );
        
        print!("\r{} [{}] {}% - {}", "⏳".bright_yellow(), bar, percentage, message);
        
        if current == total {
            println!(); // New line when complete
        }
    }

    pub fn separator(&self) {
        println!("{}", "─".repeat(60).bright_black());
    }

    pub fn highlight(&self, text: &str) -> String {
        text.bright_white().bold().to_string()
    }

    pub fn dim(&self, text: &str) -> String {
        text.bright_black().to_string()
    }

    pub fn format_duration(&self, seconds: u64) -> String {
        if seconds < 60 {
            format!("{}s", seconds.to_string().bright_yellow())
        } else {
            let minutes = seconds / 60;
            let remaining_seconds = seconds % 60;
            format!("{}m {}s", 
                minutes.to_string().bright_yellow(), 
                remaining_seconds.to_string().bright_yellow())
        }
    }
}

// Global logger instance
lazy_static::lazy_static! {
    pub static ref LOGGER: Logger = Logger::new(false);
}

// Convenience macros
#[macro_export]
macro_rules! log_info {
    ($($arg:tt)*) => {
        $crate::logger::LOGGER.info(&format!($($arg)*));
    };
}

#[macro_export]
macro_rules! log_success {
    ($($arg:tt)*) => {
        $crate::logger::LOGGER.success(&format!($($arg)*));
    };
}

#[macro_export]
macro_rules! log_warning {
    ($($arg:tt)*) => {
        $crate::logger::LOGGER.warning(&format!($($arg)*));
    };
}

#[macro_export]
macro_rules! log_error {
    ($($arg:tt)*) => {
        $crate::logger::LOGGER.error(&format!($($arg)*));
    };
}

#[macro_export]
macro_rules! log_debug {
    ($($arg:tt)*) => {
        $crate::logger::LOGGER.debug(&format!($($arg)*));
    };
}
