use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::fs;
use std::path::PathBuf;
use std::time::{SystemTime, UNIX_EPOCH};
use std::io::{self, Write};
use crate::command_planner::{CommandPlan, RiskLevel};
use crate::terminal_bridge::ExecutionResult;
use crate::logger::LOGGER;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CommandHistoryEntry {
    pub id: u64,
    pub timestamp: u64,
    pub user_input: String,
    pub plan: CommandPlan,
    pub results: Vec<ExecutionResult>,
    pub user_feedback: Option<UserFeedback>,
    pub success: bool,
    pub execution_time_total: f64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UserFeedback {
    pub rating: FeedbackRating,
    pub comment: Option<String>,
    pub would_run_again: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum FeedbackRating {
    Excellent,
    Good,
    <PERSON>,
    Poor,
    Terrible,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CommandPattern {
    pub pattern: String,
    pub success_rate: f64,
    pub average_execution_time: f64,
    pub common_failures: Vec<String>,
    pub usage_count: u32,
    pub last_used: u64,
}

pub struct CommandMemory {
    history: VecDeque<CommandHistoryEntry>,
    patterns: HashMap<String, CommandPattern>,
    max_history_size: usize,
    history_file: PathBuf,
    patterns_file: PathBuf,
    confirmation_settings: ConfirmationSettings,
}

#[derive(Debug, Clone)]
pub struct ConfirmationSettings {
    pub always_confirm_high_risk: bool,
    pub always_confirm_critical_risk: bool,
    pub confirm_destructive_operations: bool,
    pub confirm_network_operations: bool,
    pub auto_approve_safe_commands: bool,
    pub require_explicit_approval: Vec<String>,
}

impl Default for ConfirmationSettings {
    fn default() -> Self {
        Self {
            always_confirm_high_risk: true,
            always_confirm_critical_risk: true,
            confirm_destructive_operations: true,
            confirm_network_operations: true,
            auto_approve_safe_commands: true,
            require_explicit_approval: vec![
                "rm".to_string(),
                "delete".to_string(),
                "format".to_string(),
                "shutdown".to_string(),
            ],
        }
    }
}

impl CommandMemory {
    pub fn new(data_dir: PathBuf) -> Result<Self, Box<dyn std::error::Error>> {
        let history_file = data_dir.join("command_history.json");
        let patterns_file = data_dir.join("command_patterns.json");
        
        // Create data directory if it doesn't exist
        fs::create_dir_all(&data_dir)?;
        
        let mut memory = Self {
            history: VecDeque::new(),
            patterns: HashMap::new(),
            max_history_size: 1000,
            history_file,
            patterns_file,
            confirmation_settings: ConfirmationSettings::default(),
        };
        
        memory.load_history()?;
        memory.load_patterns()?;
        
        Ok(memory)
    }

    pub fn should_confirm(&self, plan: &CommandPlan) -> bool {
        // Check risk level
        match plan.risk_level {
            RiskLevel::Critical => self.confirmation_settings.always_confirm_critical_risk,
            RiskLevel::High => self.confirmation_settings.always_confirm_high_risk,
            RiskLevel::Safe => !self.confirmation_settings.auto_approve_safe_commands,
            _ => plan.requires_confirmation,
        }
    }

    pub fn request_confirmation(&self, plan: &CommandPlan) -> bool {
        LOGGER.warning(&format!("⚠️  Command requires confirmation (Risk: {})", plan.risk_level));
        LOGGER.info(&format!("Intent: {}", plan.intent));
        
        println!("\n📋 Planned Commands:");
        for (i, cmd) in plan.commands.iter().enumerate() {
            println!("  {}. {} {}", i + 1, cmd.command, cmd.args.join(" "));
            println!("     Description: {}", cmd.description);
        }
        
        // Show similar past executions
        self.show_similar_executions(plan);
        
        print!("\n❓ Do you want to execute these commands? [y/N/details]: ");
        io::stdout().flush().unwrap();
        
        let mut input = String::new();
        match io::stdin().read_line(&mut input) {
            Ok(_) => {
                let response = input.trim().to_lowercase();
                match response.as_str() {
                    "y" | "yes" => {
                        LOGGER.success("User approved command execution");
                        true
                    }
                    "details" | "d" => {
                        self.show_command_details(plan);
                        self.request_confirmation(plan) // Recursive call for details
                    }
                    _ => {
                        LOGGER.info("User declined command execution");
                        false
                    }
                }
            }
            Err(_) => {
                LOGGER.error("Failed to read user input, defaulting to decline");
                false
            }
        }
    }

    fn show_similar_executions(&self, plan: &CommandPlan) {
        let similar_commands: Vec<_> = self.history
            .iter()
            .filter(|entry| {
                // Simple similarity check based on command names
                plan.commands.iter().any(|cmd| {
                    entry.plan.commands.iter().any(|hist_cmd| {
                        hist_cmd.command == cmd.command
                    })
                })
            })
            .take(3)
            .collect();

        if !similar_commands.is_empty() {
            println!("\n📊 Similar past executions:");
            for entry in similar_commands {
                let status = if entry.success { "✅" } else { "❌" };
                println!("  {} {} - {:.1}s ago", 
                    status, 
                    entry.user_input,
                    (SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs() - entry.timestamp) as f64 / 3600.0
                );
            }
        }
    }

    fn show_command_details(&self, plan: &CommandPlan) {
        println!("\n🔍 Detailed Command Information:");
        println!("Risk Level: {}", plan.risk_level);
        println!("Intent: {}", plan.intent);
        println!("Context: Current directory: {}", plan.context.current_directory);
        
        for (i, cmd) in plan.commands.iter().enumerate() {
            println!("\nCommand {}:", i + 1);
            println!("  Command: {} {}", cmd.command, cmd.args.join(" "));
            println!("  Description: {}", cmd.description);
            if let Some(ref expected) = cmd.expected_output {
                println!("  Expected Output: {}", expected);
            }
            if let Some(timeout) = cmd.timeout_seconds {
                println!("  Timeout: {}s", timeout);
            }
            if !cmd.environment_vars.is_empty() {
                println!("  Environment Variables:");
                for (key, value) in &cmd.environment_vars {
                    println!("    {}={}", key, value);
                }
            }
        }
    }

    pub fn add_execution(&mut self, user_input: String, plan: CommandPlan, results: Vec<ExecutionResult>) {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        let success = results.iter().all(|r| r.success);
        let execution_time_total = results.iter()
            .map(|r| r.execution_time.as_secs_f64())
            .sum();

        let entry = CommandHistoryEntry {
            id: timestamp, // Simple ID based on timestamp
            timestamp,
            user_input: user_input.clone(),
            plan: plan.clone(),
            results,
            user_feedback: None,
            success,
            execution_time_total,
        };

        // Add to history
        self.history.push_back(entry);
        
        // Maintain max size
        if self.history.len() > self.max_history_size {
            self.history.pop_front();
        }

        // Update patterns
        self.update_patterns(&user_input, &plan, success, execution_time_total);
        
        // Save to disk
        if let Err(e) = self.save_history() {
            LOGGER.error(&format!("Failed to save command history: {}", e));
        }
        if let Err(e) = self.save_patterns() {
            LOGGER.error(&format!("Failed to save command patterns: {}", e));
        }
    }

    fn update_patterns(&mut self, user_input: &str, plan: &CommandPlan, success: bool, execution_time: f64) {
        let pattern_key = format!("{:?}", plan.intent);
        
        let pattern = self.patterns.entry(pattern_key).or_insert(CommandPattern {
            pattern: user_input.to_string(),
            success_rate: 0.0,
            average_execution_time: 0.0,
            common_failures: Vec::new(),
            usage_count: 0,
            last_used: 0,
        });

        // Update statistics
        let old_count = pattern.usage_count as f64;
        pattern.usage_count += 1;
        let new_count = pattern.usage_count as f64;
        
        // Update success rate (running average)
        pattern.success_rate = (pattern.success_rate * old_count + if success { 1.0 } else { 0.0 }) / new_count;
        
        // Update average execution time
        pattern.average_execution_time = (pattern.average_execution_time * old_count + execution_time) / new_count;
        
        pattern.last_used = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();
    }

    pub fn get_suggestions(&self, user_input: &str) -> Vec<String> {
        let input_lower = user_input.to_lowercase();
        let mut suggestions = Vec::new();
        
        // Find patterns with similar keywords
        for (_, pattern) in &self.patterns {
            if pattern.pattern.to_lowercase().contains(&input_lower) || 
               input_lower.contains(&pattern.pattern.to_lowercase()) {
                if pattern.success_rate > 0.7 { // Only suggest successful patterns
                    suggestions.push(pattern.pattern.clone());
                }
            }
        }
        
        // Sort by success rate and usage count
        suggestions.sort_by(|a, b| {
            let pattern_a = self.patterns.values().find(|p| p.pattern == *a);
            let pattern_b = self.patterns.values().find(|p| p.pattern == *b);
            
            match (pattern_a, pattern_b) {
                (Some(a), Some(b)) => {
                    b.success_rate.partial_cmp(&a.success_rate)
                        .unwrap_or(std::cmp::Ordering::Equal)
                        .then(b.usage_count.cmp(&a.usage_count))
                }
                _ => std::cmp::Ordering::Equal,
            }
        });
        
        suggestions.truncate(5); // Limit to top 5 suggestions
        suggestions
    }

    pub fn show_history(&self, limit: Option<usize>) {
        let limit = limit.unwrap_or(10);
        let recent_entries: Vec<_> = self.history.iter().rev().take(limit).collect();
        
        if recent_entries.is_empty() {
            LOGGER.info("No command history available");
            return;
        }
        
        LOGGER.section("Command History");
        
        for entry in recent_entries {
            let status = if entry.success { "✅" } else { "❌" };
            let time_ago = (SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs() - entry.timestamp) as f64 / 60.0;
            
            println!("{} {} ({:.1}m ago) - {:.2}s", 
                status, 
                entry.user_input,
                time_ago,
                entry.execution_time_total
            );
        }
    }

    fn load_history(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if self.history_file.exists() {
            let content = fs::read_to_string(&self.history_file)?;
            self.history = serde_json::from_str(&content)?;
            LOGGER.debug(&format!("Loaded {} command history entries", self.history.len()));
        }
        Ok(())
    }

    fn save_history(&self) -> Result<(), Box<dyn std::error::Error>> {
        let content = serde_json::to_string_pretty(&self.history)?;
        fs::write(&self.history_file, content)?;
        Ok(())
    }

    fn load_patterns(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if self.patterns_file.exists() {
            let content = fs::read_to_string(&self.patterns_file)?;
            self.patterns = serde_json::from_str(&content)?;
            LOGGER.debug(&format!("Loaded {} command patterns", self.patterns.len()));
        }
        Ok(())
    }

    fn save_patterns(&self) -> Result<(), Box<dyn std::error::Error>> {
        let content = serde_json::to_string_pretty(&self.patterns)?;
        fs::write(&self.patterns_file, content)?;
        Ok(())
    }
}
