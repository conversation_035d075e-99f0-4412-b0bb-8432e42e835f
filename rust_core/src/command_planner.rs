use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::logger::LOGGER;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandPlan {
    pub intent: CommandIntent,
    pub commands: Vec<PlannedCommand>,
    pub context: CommandContext,
    pub risk_level: RiskLevel,
    pub requires_confirmation: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PlannedCommand {
    pub command: String,
    pub args: Vec<String>,
    pub description: String,
    pub expected_output: Option<String>,
    pub working_directory: Option<String>,
    pub environment_vars: HashMap<String, String>,
    pub timeout_seconds: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CommandIntent {
    FileOperation,
    ProcessManagement,
    SystemInfo,
    NetworkOperation,
    Development,
    CodeGeneration,
    Testing,
    Configuration,
    Unknown,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CommandContext {
    pub current_directory: String,
    pub available_tools: Vec<String>,
    pub recent_commands: Vec<String>,
    pub project_type: Option<String>,
    pub language_context: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum RiskLevel {
    Safe,      // Read-only operations, info commands
    Low,       // File creation, basic operations
    Medium,    // File modification, process management
    High,      // System changes, network operations
    Critical,  // Destructive operations, system admin
}

pub struct CommandPlanner {
    context: CommandContext,
    command_patterns: HashMap<String, Vec<CommandTemplate>>,
}

#[derive(Debug, Clone)]
struct CommandTemplate {
    pattern: String,
    intent: CommandIntent,
    risk_level: RiskLevel,
    command_builder: fn(&str, &CommandContext) -> Vec<PlannedCommand>,
}

impl CommandPlanner {
    pub fn new(context: CommandContext) -> Self {
        let mut planner = Self {
            context,
            command_patterns: HashMap::new(),
        };
        planner.initialize_patterns();
        planner
    }

    pub fn plan_command(&self, user_input: &str) -> CommandPlan {
        LOGGER.debug(&format!("Planning command for input: {}", user_input));
        
        let intent = self.analyze_intent(user_input);
        let risk_level = self.assess_risk(user_input, &intent);
        let commands = self.generate_commands(user_input, &intent);
        
        CommandPlan {
            intent: intent.clone(),
            commands,
            context: self.context.clone(),
            risk_level: risk_level.clone(),
            requires_confirmation: matches!(risk_level, RiskLevel::High | RiskLevel::Critical),
        }
    }

    fn analyze_intent(&self, input: &str) -> CommandIntent {
        let input_lower = input.to_lowercase();
        
        // File operations
        if input_lower.contains("create file") || input_lower.contains("write file") 
            || input_lower.contains("delete file") || input_lower.contains("copy file") {
            return CommandIntent::FileOperation;
        }
        
        // Process management
        if input_lower.contains("kill process") || input_lower.contains("start process")
            || input_lower.contains("stop service") || input_lower.contains("restart") {
            return CommandIntent::ProcessManagement;
        }
        
        // System info
        if input_lower.contains("system info") || input_lower.contains("disk space")
            || input_lower.contains("memory usage") || input_lower.contains("cpu usage") {
            return CommandIntent::SystemInfo;
        }
        
        // Development
        if input_lower.contains("compile") || input_lower.contains("build")
            || input_lower.contains("run tests") || input_lower.contains("git") {
            return CommandIntent::Development;
        }
        
        // Code generation (our main use case)
        if input_lower.contains("generate") || input_lower.contains("create function")
            || input_lower.contains("write code") || input_lower.contains("implement") {
            return CommandIntent::CodeGeneration;
        }
        
        CommandIntent::Unknown
    }

    fn assess_risk(&self, input: &str, intent: &CommandIntent) -> RiskLevel {
        let input_lower = input.to_lowercase();
        
        // Critical risk keywords
        if input_lower.contains("rm -rf") || input_lower.contains("format")
            || input_lower.contains("delete all") || input_lower.contains("sudo rm") {
            return RiskLevel::Critical;
        }
        
        // High risk keywords
        if input_lower.contains("sudo") || input_lower.contains("chmod 777")
            || input_lower.contains("kill -9") || input_lower.contains("shutdown") {
            return RiskLevel::High;
        }
        
        // Medium risk based on intent
        match intent {
            CommandIntent::ProcessManagement => RiskLevel::Medium,
            CommandIntent::FileOperation => {
                if input_lower.contains("delete") || input_lower.contains("remove") {
                    RiskLevel::Medium
                } else {
                    RiskLevel::Low
                }
            }
            CommandIntent::SystemInfo => RiskLevel::Safe,
            CommandIntent::CodeGeneration => RiskLevel::Low,
            CommandIntent::Development => RiskLevel::Low,
            _ => RiskLevel::Low,
        }
    }

    fn generate_commands(&self, input: &str, intent: &CommandIntent) -> Vec<PlannedCommand> {
        match intent {
            CommandIntent::CodeGeneration => self.generate_code_commands(input),
            CommandIntent::FileOperation => self.generate_file_commands(input),
            CommandIntent::SystemInfo => self.generate_system_info_commands(input),
            CommandIntent::Development => self.generate_dev_commands(input),
            _ => self.generate_generic_commands(input),
        }
    }

    fn generate_code_commands(&self, input: &str) -> Vec<PlannedCommand> {
        vec![
            PlannedCommand {
                command: "python3".to_string(),
                args: vec![
                    "py_helpers/gen_code.py".to_string(),
                    input.to_string(),
                    "deepseek-coder:1.5b".to_string(),
                    self.context.language_context.clone().unwrap_or("python".to_string()),
                    "None specified".to_string(),
                ],
                description: format!("Generate code for: {}", input),
                expected_output: Some("Generated code file".to_string()),
                working_directory: None,
                environment_vars: HashMap::new(),
                timeout_seconds: Some(60),
            }
        ]
    }

    fn generate_file_commands(&self, input: &str) -> Vec<PlannedCommand> {
        // Parse file operations from natural language
        if input.to_lowercase().contains("list files") {
            vec![
                PlannedCommand {
                    command: "ls".to_string(),
                    args: vec!["-la".to_string()],
                    description: "List files in current directory".to_string(),
                    expected_output: Some("Directory listing".to_string()),
                    working_directory: None,
                    environment_vars: HashMap::new(),
                    timeout_seconds: Some(5),
                }
            ]
        } else {
            vec![]
        }
    }

    fn generate_system_info_commands(&self, input: &str) -> Vec<PlannedCommand> {
        let input_lower = input.to_lowercase();
        
        if input_lower.contains("disk space") {
            vec![
                PlannedCommand {
                    command: "df".to_string(),
                    args: vec!["-h".to_string()],
                    description: "Show disk space usage".to_string(),
                    expected_output: Some("Disk usage information".to_string()),
                    working_directory: None,
                    environment_vars: HashMap::new(),
                    timeout_seconds: Some(10),
                }
            ]
        } else if input_lower.contains("memory") {
            vec![
                PlannedCommand {
                    command: "free".to_string(),
                    args: vec!["-h".to_string()],
                    description: "Show memory usage".to_string(),
                    expected_output: Some("Memory usage information".to_string()),
                    working_directory: None,
                    environment_vars: HashMap::new(),
                    timeout_seconds: Some(10),
                }
            ]
        } else {
            vec![]
        }
    }

    fn generate_dev_commands(&self, input: &str) -> Vec<PlannedCommand> {
        let input_lower = input.to_lowercase();
        
        if input_lower.contains("git status") {
            vec![
                PlannedCommand {
                    command: "git".to_string(),
                    args: vec!["status".to_string()],
                    description: "Show git repository status".to_string(),
                    expected_output: Some("Git status information".to_string()),
                    working_directory: None,
                    environment_vars: HashMap::new(),
                    timeout_seconds: Some(10),
                }
            ]
        } else {
            vec![]
        }
    }

    fn generate_generic_commands(&self, _input: &str) -> Vec<PlannedCommand> {
        vec![]
    }

    fn initialize_patterns(&mut self) {
        // This would be expanded with more sophisticated pattern matching
        // For now, we rely on the intent analysis and command generation methods
    }

    pub fn update_context(&mut self, context: CommandContext) {
        self.context = context;
    }
}

impl std::fmt::Display for CommandIntent {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CommandIntent::FileOperation => write!(f, "File Operation"),
            CommandIntent::ProcessManagement => write!(f, "Process Management"),
            CommandIntent::SystemInfo => write!(f, "System Information"),
            CommandIntent::NetworkOperation => write!(f, "Network Operation"),
            CommandIntent::Development => write!(f, "Development"),
            CommandIntent::CodeGeneration => write!(f, "Code Generation"),
            CommandIntent::Testing => write!(f, "Testing"),
            CommandIntent::Configuration => write!(f, "Configuration"),
            CommandIntent::Unknown => write!(f, "Unknown"),
        }
    }
}

impl std::fmt::Display for RiskLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RiskLevel::Safe => write!(f, "Safe"),
            RiskLevel::Low => write!(f, "Low Risk"),
            RiskLevel::Medium => write!(f, "Medium Risk"),
            RiskLevel::High => write!(f, "High Risk"),
            RiskLevel::Critical => write!(f, "Critical Risk"),
        }
    }
}
