use crate::logger::LOGGER;
use crate::language::list_supported_languages;

pub fn show_comprehensive_help() {
    LOGGER.section("Alim Agent - AI-Powered Code Generation CLI");
    
    println!("Alim Agent is a powerful CLI tool that generates, compiles, and executes code");
    println!("in multiple programming languages using AI models via Ollama.");
    println!();
    
    show_basic_usage();
    show_interactive_mode_help();
    show_configuration_help();
    show_examples();
    show_troubleshooting();
    show_advanced_features();
}

pub fn show_basic_usage() {
    LOGGER.section("Basic Usage");
    
    println!("  # Generate and run Python code");
    println!("  alim -t \"create a fibonacci function\" -l python");
    println!();
    println!("  # Use a specific model");
    println!("  alim -t \"web server with error handling\" -m deepseek-coder:6.7b");
    println!();
    println!("  # Set execution timeout");
    println!("  alim -t \"complex algorithm\" --timeout 60");
    println!();
    println!("  # Enable testing");
    println!("  alim -t \"sorting function\" --test");
    println!();
    println!("  # Add constraints");
    println!("  alim -t \"calculator\" -c \"use only basic operations, no imports\"");
    println!();
}

pub fn show_interactive_mode_help() {
    LOGGER.section("Interactive Mode");
    
    println!("Start interactive mode with: alim -i");
    println!();
    println!("Available commands in interactive mode:");
    println!("  • Enter any coding task to generate and run code");
    println!("  • 'help' - Show help message");
    println!("  • 'status' - Show current settings");
    println!("  • 'exit' or 'quit' - Exit interactive mode");
    println!();
    println!("Configuration commands:");
    println!("  • /lang <language> - Change programming language");
    println!("  • /model <model> - Change AI model");
    println!("  • /timeout <seconds> - Change execution timeout");
    println!("  • /test on|off - Enable/disable testing");
    println!("  • /languages - List supported languages");
    println!("  • /config [show|save|reset] - Manage configuration");
    println!("  • /config set <key> <value> - Set config value");
    println!("  • /config lang <lang> <field> <value> - Set language-specific config");
    println!();
}

pub fn show_configuration_help() {
    LOGGER.section("Configuration Management");
    
    println!("Alim Agent supports persistent configuration through JSON files.");
    println!("Config file location: ~/.config/alim/config.json");
    println!();
    println!("Configuration options:");
    println!("  • default_language - Default programming language");
    println!("  • default_model - Default AI model");
    println!("  • default_timeout - Default execution timeout (seconds)");
    println!("  • enable_testing - Enable testing by default");
    println!("  • enable_debug - Enable debug mode");
    println!("  • auto_save_code - Automatically save generated code");
    println!("  • output_directory - Directory for saved code");
    println!("  • favorite_models - List of preferred models");
    println!("  • language_preferences - Language-specific settings");
    println!();
    println!("Interactive config commands:");
    println!("  /config show - Display current configuration");
    println!("  /config save - Save current settings to file");
    println!("  /config reset - Reset to default settings");
    println!("  /config set timeout 45 - Set default timeout");
    println!("  /config lang rust model deepseek-coder:6.7b - Set Rust-specific model");
    println!();
}

pub fn show_examples() {
    LOGGER.section("Examples");
    
    println!("🐍 Python Examples:");
    println!("  alim -t \"web scraper using requests\" -l python");
    println!("  alim -t \"data analysis with pandas\" -l python --test");
    println!("  alim -t \"REST API with Flask\" -l python --timeout 45");
    println!();
    
    println!("🦀 Rust Examples:");
    println!("  alim -t \"concurrent file processor\" -l rust");
    println!("  alim -t \"CLI tool with clap\" -l rust --test");
    println!("  alim -t \"web server with tokio\" -l rust --timeout 60");
    println!();
    
    println!("🌐 JavaScript Examples:");
    println!("  alim -t \"React component with hooks\" -l javascript");
    println!("  alim -t \"Node.js Express server\" -l javascript");
    println!("  alim -t \"async data fetching\" -l javascript --test");
    println!();
    
    println!("📝 TypeScript Examples:");
    println!("  alim -t \"type-safe API client\" -l typescript");
    println!("  alim -t \"generic utility functions\" -l typescript --test");
    println!();
    
    println!("🚀 Go Examples:");
    println!("  alim -t \"HTTP server with middleware\" -l go");
    println!("  alim -t \"concurrent worker pool\" -l go --test");
    println!();
    
    println!("☕ Java Examples:");
    println!("  alim -t \"Spring Boot REST API\" -l java");
    println!("  alim -t \"multithreaded application\" -l java --timeout 50");
    println!();
}

pub fn show_troubleshooting() {
    LOGGER.section("Troubleshooting");
    
    println!("Common issues and solutions:");
    println!();
    println!("❌ \"Connection refused\" error:");
    println!("   • Make sure Ollama is running: ollama serve");
    println!("   • Check if the service is accessible: curl http://localhost:11434");
    println!();
    println!("❌ \"Model not found\" error:");
    println!("   • List available models: ollama list");
    println!("   • Pull the model: ollama pull deepseek-coder:1.5b");
    println!();
    println!("❌ Code compilation fails:");
    println!("   • Check if the language compiler is installed");
    println!("   • Try with simpler requirements or different constraints");
    println!("   • Use --test flag to validate generated code");
    println!();
    println!("❌ Timeout errors:");
    println!("   • Increase timeout: --timeout 60");
    println!("   • Try a smaller, more specific task");
    println!("   • Use a faster model like deepseek-coder:1.5b");
    println!();
    println!("❌ Generated code quality issues:");
    println!("   • Add specific constraints: -c \"include error handling\"");
    println!("   • Try a larger model: -m deepseek-coder:6.7b");
    println!("   • Use interactive mode for iterative refinement");
    println!();
}

pub fn show_advanced_features() {
    LOGGER.section("Advanced Features");
    
    println!("🧪 Testing System:");
    println!("   • Automatic test generation and execution");
    println!("   • Language-specific test frameworks");
    println!("   • Configurable test scenarios");
    println!();
    
    println!("⏱️ Execution Control:");
    println!("   • Configurable timeouts for compilation and execution");
    println!("   • Resource limits to prevent runaway processes");
    println!("   • Graceful error handling and recovery");
    println!();
    
    println!("🎨 Output Formatting:");
    println!("   • Colored, structured output");
    println!("   • Progress indicators and status updates");
    println!("   • Detailed error messages with suggestions");
    println!();
    
    println!("🔧 Extensibility:");
    println!("   • Template-based prompt system");
    println!("   • Language-specific configurations");
    println!("   • Modular architecture for easy extension");
    println!();
    
    println!("📊 Code Quality:");
    println!("   • Enhanced prompts with best practices");
    println!("   • Retry logic with feedback loops");
    println!("   • Code validation and quality checks");
    println!();
}

pub fn show_supported_languages() {
    LOGGER.section("Supported Programming Languages");
    list_supported_languages();
    println!();
    println!("Each language includes:");
    println!("  • Automatic compilation (if needed)");
    println!("  • Execution with timeout controls");
    println!("  • Language-specific best practices");
    println!("  • Appropriate test frameworks");
    println!();
}

pub fn show_model_recommendations() {
    LOGGER.section("Model Recommendations");
    
    println!("🚀 Fast Development (deepseek-coder:1.5b):");
    println!("   • Quick responses for simple tasks");
    println!("   • Good for prototyping and learning");
    println!("   • Lower resource requirements");
    println!();
    
    println!("⚡ Balanced Performance (deepseek-coder:6.7b):");
    println!("   • Better code quality and complexity handling");
    println!("   • Good for production-ready code");
    println!("   • Recommended for most use cases");
    println!();
    
    println!("🎯 Specialized Tasks (codellama:7b/13b):");
    println!("   • Alternative model family");
    println!("   • Good for specific language tasks");
    println!("   • May have different strengths");
    println!();
    
    println!("💡 Tips:");
    println!("   • Start with smaller models for faster iteration");
    println!("   • Use larger models for complex, production code");
    println!("   • Configure language-specific model preferences");
    println!();
}

pub fn show_quick_start() {
    LOGGER.section("Quick Start Guide");
    
    println!("1. 🚀 Install and start Ollama:");
    println!("   curl -fsSL https://ollama.ai/install.sh | sh");
    println!("   ollama serve");
    println!();
    
    println!("2. 📥 Pull a code generation model:");
    println!("   ollama pull deepseek-coder:1.5b");
    println!();
    
    println!("3. 🎯 Generate your first code:");
    println!("   alim -t \"hello world function\" -l python");
    println!();
    
    println!("4. 🔧 Try interactive mode:");
    println!("   alim -i");
    println!();
    
    println!("5. ⚙️ Configure your preferences:");
    println!("   alim -i");
    println!("   /config set default_language rust");
    println!("   /config save");
    println!();
}

pub fn show_help_menu() {
    LOGGER.section("Help Topics");
    
    println!("Available help topics:");
    println!("  • basic - Basic usage and command-line options");
    println!("  • interactive - Interactive mode commands");
    println!("  • config - Configuration management");
    println!("  • examples - Usage examples for different languages");
    println!("  • troubleshooting - Common issues and solutions");
    println!("  • advanced - Advanced features and capabilities");
    println!("  • languages - Supported programming languages");
    println!("  • models - Model recommendations and tips");
    println!("  • quickstart - Quick start guide");
    println!("  • comprehensive - Complete documentation (all topics)");
    println!();
    println!("Usage: alim --help <topic>");
    println!("   or: /help <topic> (in interactive mode)");
    println!();
}

pub fn show_topic_help(topic: &str) {
    match topic.to_lowercase().as_str() {
        "basic" | "usage" => show_basic_usage(),
        "interactive" | "interactive-mode" => show_interactive_mode_help(),
        "config" | "configuration" => show_configuration_help(),
        "examples" => show_examples(),
        "troubleshooting" | "troubleshoot" => show_troubleshooting(),
        "advanced" => show_advanced_features(),
        "languages" | "langs" => show_supported_languages(),
        "models" => show_model_recommendations(),
        "quickstart" | "quick-start" => show_quick_start(),
        "comprehensive" | "all" => show_comprehensive_help(),
        _ => {
            LOGGER.error(&format!("Unknown help topic: {}", topic));
            show_help_menu();
        }
    }
}
