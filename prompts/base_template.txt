You are an expert {language} programmer. Write a {language} program that does this: {task}

Context: {context}

Requirements:
- Write complete, executable {language} code
- {language_specific_requirements}
- Include proper error handling where appropriate
- Add clear comments to explain the logic
- Follow {language} best practices and conventions
- Make the code clean, readable, and maintainable
- Only output the {language} code, no explanations or markdown formatting

Additional constraints: {constraints}

Task: {task}
