{"python": {"extension": "py", "context": "Python is a high-level, interpreted programming language known for its simplicity and readability.", "language_specific_requirements": "Use Python 3.x syntax and features\n- Use type hints where appropriate\n- Follow PEP 8 style guidelines\n- Use proper exception handling with try/except blocks\n- Include docstrings for functions and classes", "execution_command": ["python3"], "compile_command": null}, "rust": {"extension": "rs", "context": "Rust is a systems programming language focused on safety, speed, and concurrency.", "language_specific_requirements": "Use safe Rust practices\n- Handle errors with Result types\n- Use proper ownership and borrowing\n- Include a main function\n- Use cargo conventions for project structure", "execution_command": ["./temp_executable"], "compile_command": ["rustc", "--edition=2021", "-o", "temp_executable"]}, "javascript": {"extension": "js", "context": "JavaScript is a dynamic programming language commonly used for web development.", "language_specific_requirements": "Use modern ES6+ features\n- Use const/let instead of var\n- Use arrow functions where appropriate\n- Handle promises and async operations properly\n- Use proper error handling with try/catch", "execution_command": ["node"], "compile_command": null}, "typescript": {"extension": "ts", "context": "TypeScript is a typed superset of JavaScript that compiles to plain JavaScript.", "language_specific_requirements": "Use proper type annotations\n- Define interfaces for complex objects\n- Use generics where appropriate\n- Enable strict type checking\n- Use modern ES6+ features", "execution_command": ["npx", "ts-node"], "compile_command": null}, "go": {"extension": "go", "context": "Go is a statically typed, compiled programming language designed for simplicity and efficiency.", "language_specific_requirements": "Include package main and main function\n- Use proper Go error handling patterns\n- Follow Go naming conventions (exported vs unexported)\n- Use gofmt formatting\n- Handle errors explicitly", "execution_command": ["go", "run"], "compile_command": null}, "java": {"extension": "java", "context": "Java is a class-based, object-oriented programming language designed for portability.", "language_specific_requirements": "Include a proper class with main method\n- Use appropriate access modifiers (public, private, protected)\n- Follow Java naming conventions (CamelCase for classes, camelCase for methods)\n- Use proper exception handling\n- Include package declaration if needed", "execution_command": ["java"], "compile_command": ["javac"]}, "csharp": {"extension": "cs", "context": "C# is a modern, object-oriented programming language developed by Microsoft.", "language_specific_requirements": "Use proper namespace and class structure\n- Include using statements for required namespaces\n- Follow C# naming conventions (PascalCase for public members)\n- Use proper exception handling with try/catch\n- Include Main method as entry point", "execution_command": ["dotnet", "run"], "compile_command": null}, "cpp": {"extension": "cpp", "context": "C++ is a general-purpose programming language with object-oriented and generic programming features.", "language_specific_requirements": "Use modern C++ features (C++17 or later)\n- Include necessary headers\n- Use proper memory management (RAII, smart pointers)\n- Include main function\n- Use namespace std appropriately", "execution_command": ["./temp_executable"], "compile_command": ["g++", "-std=c++17", "-o", "temp_executable"]}, "c": {"extension": "c", "context": "C is a general-purpose, procedural programming language supporting structured programming.", "language_specific_requirements": "Include necessary headers\n- Use proper memory management (malloc/free)\n- Include main function\n- Use proper variable declarations\n- Handle errors appropriately", "execution_command": ["./temp_executable"], "compile_command": ["gcc", "-std=c99", "-o", "temp_executable"]}}