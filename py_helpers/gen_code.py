import sys, requests
import json
import time
import re
from prompt_manager import PromptManager

class CodeGenerator:
    def __init__(self, model="deepseek-coder:1.5b", max_retries=3, base_timeout=60):
        self.model = model
        self.max_retries = max_retries
        self.base_timeout = base_timeout
        self.prompt_manager = PromptManager()

    def generate_code(self, prompt, attempt=1):
        """Generate code with retry logic and enhanced error handling"""
        try:
            print(f"🔗 Connecting to model: {self.model} (attempt {attempt}/{self.max_retries})")

            # Adaptive timeout based on attempt
            timeout = self.base_timeout * attempt

            res = requests.post("http://assistant.undeclab.com:11434/api/generate", json={
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.1,  # Lower temperature for more consistent code
                    "top_p": 0.9,
                    "top_k": 40,
                    "num_predict": 2048,  # Allow longer responses
                }
            }, timeout=timeout)

            if res.status_code == 200:
                response_data = res.json()
                code = response_data.get("response", "")

                if self.validate_code_response(code):
                    return code
                else:
                    print(f"⚠️ Generated code failed validation on attempt {attempt}")
                    if attempt < self.max_retries:
                        return self.retry_with_feedback(prompt, code, attempt)
                    else:
                        print("❌ Max retries reached. Returning best attempt.")
                        return code
            else:
                print(f"❌ API Error: {res.status_code} - {res.text}")
                if attempt < self.max_retries and res.status_code >= 500:
                    print(f"🔄 Retrying in {attempt * 2} seconds...")
                    time.sleep(attempt * 2)
                    return self.generate_code(prompt, attempt + 1)
                return None

        except requests.exceptions.Timeout:
            print(f"⏰ Request timed out after {timeout}s")
            if attempt < self.max_retries:
                print(f"🔄 Retrying with longer timeout...")
                return self.generate_code(prompt, attempt + 1)
            return None

        except requests.exceptions.ConnectionError as e:
            print(f"❌ Connection Error: {e}")
            print("💡 Make sure Ollama is running: ollama serve")
            if attempt < self.max_retries:
                print(f"🔄 Retrying in {attempt * 3} seconds...")
                time.sleep(attempt * 3)
                return self.generate_code(prompt, attempt + 1)
            return None

        except requests.exceptions.RequestException as e:
            print(f"❌ Request Error: {e}")
            return None

        except json.JSONDecodeError as e:
            print(f"❌ JSON Decode Error: {e}")
            if attempt < self.max_retries:
                return self.generate_code(prompt, attempt + 1)
            return None

    def validate_code_response(self, code):
        """Basic validation of generated code"""
        if not code or len(code.strip()) < 10:
            return False

        # Check if response contains actual code (not just explanation)
        code_indicators = [
            'def ', 'function ', 'class ', 'import ', 'from ',
            '{', '}', '()', 'return', 'print', 'console.log',
            'fn ', 'let ', 'const ', 'var ', 'public ', 'private'
        ]

        return any(indicator in code.lower() for indicator in code_indicators)

    def retry_with_feedback(self, original_prompt, failed_code, attempt):
        """Retry with feedback about the previous attempt"""
        feedback_prompt = f"""
{original_prompt}

IMPORTANT: The previous attempt generated this code which had issues:
```
{failed_code[:500]}...
```

Please generate a better, more complete solution that:
1. Is syntactically correct
2. Includes proper error handling
3. Has clear, readable code structure
4. Includes comments explaining key parts
5. Follows best practices for the target language

Generate ONLY the code, no explanations:
"""

        print(f"🔄 Retrying with feedback (attempt {attempt + 1})...")
        time.sleep(1)  # Brief pause before retry
        return self.generate_code(feedback_prompt, attempt + 1)

    def extract_code_blocks(self, response):
        """Extract code from markdown code blocks if present"""
        # Look for code blocks with language specification
        code_block_pattern = r'```(?:\w+)?\n(.*?)\n```'
        matches = re.findall(code_block_pattern, response, re.DOTALL)

        if matches:
            # Return the largest code block (likely the main code)
            return max(matches, key=len).strip()

        # If no code blocks, return the response as-is
        return response.strip()

    def enhance_prompt_with_examples(self, task, language, constraints):
        """Enhance the prompt with language-specific examples and best practices"""
        base_prompt = self.prompt_manager.generate_prompt(task, language, constraints)

        # Add language-specific enhancement
        enhancement = self.get_language_enhancement(language)

        enhanced_prompt = f"""
{base_prompt}

{enhancement}

CRITICAL REQUIREMENTS:
1. Generate ONLY executable code - no explanations or markdown
2. Include proper error handling and edge cases
3. Use meaningful variable and function names
4. Add brief comments for complex logic
5. Follow the language's best practices and conventions
6. Make the code production-ready and robust

Code:
"""
        return enhanced_prompt

    def get_language_enhancement(self, language):
        """Get language-specific enhancements for prompts"""
        enhancements = {
            'python': """
PYTHON BEST PRACTICES:
- Use type hints where appropriate
- Follow PEP 8 style guidelines
- Include docstrings for functions
- Use context managers (with statements) for resources
- Handle exceptions appropriately
- Use list comprehensions and generators when suitable
""",
            'rust': """
RUST BEST PRACTICES:
- Use proper ownership and borrowing
- Handle Result and Option types correctly
- Include proper error handling with ? operator
- Use idiomatic Rust patterns
- Add appropriate derive macros
- Use cargo fmt style
""",
            'javascript': """
JAVASCRIPT BEST PRACTICES:
- Use modern ES6+ syntax
- Include proper error handling with try/catch
- Use const/let instead of var
- Add JSDoc comments for functions
- Handle async operations properly
- Use meaningful variable names
""",
            'typescript': """
TYPESCRIPT BEST PRACTICES:
- Use proper type annotations
- Define interfaces for complex objects
- Use union types and generics appropriately
- Handle null/undefined cases
- Use strict mode settings
- Include proper error handling
"""
        }

        return enhancements.get(language.lower(), "Follow best practices for the target language.")

def generate_code(prompt, model="deepseek-coder:1.5b"):
    """Legacy function for backward compatibility"""
    generator = CodeGenerator(model)
    return generator.generate_code(prompt)



if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("❌ Usage: python gen_code.py <task> [model] [language] [constraints]")
        sys.exit(1)

    task = sys.argv[1]
    model = sys.argv[2] if len(sys.argv) > 2 else "deepseek-coder:1.5b"
    language = sys.argv[3] if len(sys.argv) > 3 else "python"
    constraints = sys.argv[4] if len(sys.argv) > 4 else "None specified"

    print(f"📝 Task: {task}")
    print(f"🔧 Language: {language}")
    print(f"📋 Model: {model}")

    if constraints != "None specified":
        print(f"⚙️ Constraints: {constraints}")

    # Initialize enhanced code generator
    generator = CodeGenerator(model, max_retries=3)

    # Generate enhanced prompt with examples and best practices
    enhanced_prompt = generator.enhance_prompt_with_examples(task, language, constraints)

    print("🔄 Generating enhanced code with retry logic...")

    # Generate code with enhanced error handling
    code = generator.generate_code(enhanced_prompt)

    if code:
        # Extract code from markdown blocks if present
        clean_code = generator.extract_code_blocks(code)

        # Get language info for file extension
        lang_info = generator.prompt_manager.get_language_info(language)
        filename = f"../shared/generated_code.{lang_info['extension']}"

        with open(filename, "w") as f:
            f.write(clean_code)

        print("✅ Code generated successfully!")
        print(f"💾 Code saved to {filename}")

        # Additional validation
        if len(clean_code.strip()) < 20:
            print("⚠️ Warning: Generated code seems very short. Consider retrying with more specific requirements.")

    else:
        print("❌ Failed to generate code after all retry attempts")
        print("💡 Try:")
        print("   - Check if Ollama is running: ollama serve")
        print("   - Verify the model is available: ollama list")
        print("   - Try a different model or simpler task")
        sys.exit(1)
