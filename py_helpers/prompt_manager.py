import json
import os
from string import Template

class PromptManager:
    def __init__(self, prompts_dir=None):
        if prompts_dir is None:
            # Default to prompts directory relative to project root
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            self.prompts_dir = os.path.join(project_root, "prompts")
        else:
            self.prompts_dir = prompts_dir
        self.language_configs = self._load_language_configs()
        self.base_template = self._load_base_template()
    
    def _load_language_configs(self):
        """Load language-specific configurations"""
        config_path = os.path.join(self.prompts_dir, "language_configs.json")
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️  Warning: {config_path} not found, using fallback configs")
            return self._get_fallback_configs()
    
    def _load_base_template(self):
        """Load the base prompt template"""
        template_path = os.path.join(self.prompts_dir, "base_template.txt")
        try:
            with open(template_path, 'r') as f:
                return Template(f.read())
        except FileNotFoundError:
            print(f"⚠️  Warning: {template_path} not found, using fallback template")
            return Template(self._get_fallback_template())
    
    def _get_fallback_configs(self):
        """Fallback language configurations if file is missing"""
        return {
            "python": {
                "extension": "py",
                "context": "Python is a high-level, interpreted programming language.",
                "language_specific_requirements": "Use Python 3.x syntax and follow PEP 8 guidelines"
            }
        }
    
    def _get_fallback_template(self):
        """Fallback template if file is missing"""
        return """Write a {language} program that does this: {task}

Requirements:
- Write complete, executable {language} code
- {language_specific_requirements}
- Include proper error handling
- Add comments to explain the logic
- Only output the code, no explanations

Task: {task}"""
    
    def generate_prompt(self, task, language, constraints="None specified"):
        """Generate a complete prompt for the given task and language"""
        language_lower = language.lower()
        
        if language_lower not in self.language_configs:
            print(f"⚠️  Warning: No config found for {language}, using Python defaults")
            language_lower = "python"
        
        config = self.language_configs[language_lower]
        
        # Prepare template variables
        template_vars = {
            "task": task,
            "language": language,
            "context": config.get("context", ""),
            "language_specific_requirements": config.get("language_specific_requirements", ""),
            "constraints": constraints
        }
        
        # Generate the prompt
        try:
            prompt = self.base_template.substitute(template_vars)
            return prompt
        except KeyError as e:
            print(f"❌ Template error: Missing variable {e}")
            return self._generate_simple_prompt(task, language)
    
    def _generate_simple_prompt(self, task, language):
        """Generate a simple prompt as fallback"""
        return f"Write a {language} program that does this: {task}. Only output the code."
    
    def get_language_info(self, language):
        """Get language configuration info"""
        language_lower = language.lower()
        return self.language_configs.get(language_lower, self.language_configs["python"])
    
    def list_supported_languages(self):
        """List all supported languages"""
        return list(self.language_configs.keys())

# Example usage and testing
if __name__ == "__main__":
    pm = PromptManager()
    
    # Test prompt generation
    task = "create a function to calculate fibonacci numbers"
    language = "Python"
    
    prompt = pm.generate_prompt(task, language)
    print("Generated Prompt:")
    print("=" * 50)
    print(prompt)
    print("=" * 50)
    
    # Test language info
    info = pm.get_language_info("rust")
    print(f"\nRust info: {info}")
    
    # List languages
    print(f"\nSupported languages: {pm.list_supported_languages()}")
