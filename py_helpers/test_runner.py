import json
import subprocess
import sys
import os
import tempfile
from typing import Dict, List, Any, Optional

class TestCase:
    def __init__(self, name: str, input_data: Any = None, expected_output: Any = None, 
                 expected_error: Optional[str] = None, timeout: int = 10):
        self.name = name
        self.input_data = input_data
        self.expected_output = expected_output
        self.expected_error = expected_error
        self.timeout = timeout

class TestRunner:
    def __init__(self, language: str = "python"):
        self.language = language.lower()
        self.results = []
    
    def run_tests(self, code_file: str, test_cases: List[TestCase]) -> Dict[str, Any]:
        """Run all test cases against the generated code"""
        results = {
            "total_tests": len(test_cases),
            "passed": 0,
            "failed": 0,
            "errors": 0,
            "test_results": []
        }
        
        for test_case in test_cases:
            result = self._run_single_test(code_file, test_case)
            results["test_results"].append(result)
            
            if result["status"] == "PASSED":
                results["passed"] += 1
            elif result["status"] == "FAILED":
                results["failed"] += 1
            else:
                results["errors"] += 1
        
        return results
    
    def _run_single_test(self, code_file: str, test_case: TestCase) -> Dict[str, Any]:
        """Run a single test case"""
        try:
            # Prepare input if needed
            input_str = ""
            if test_case.input_data is not None:
                if isinstance(test_case.input_data, str):
                    input_str = test_case.input_data
                else:
                    input_str = str(test_case.input_data)
            
            # Execute the code
            if self.language == "python":
                cmd = ["python3", code_file]
            elif self.language == "javascript":
                cmd = ["node", code_file]
            elif self.language == "rust":
                # Assume compiled executable
                cmd = ["./temp_executable"]
            else:
                return {
                    "name": test_case.name,
                    "status": "ERROR",
                    "message": f"Unsupported language: {self.language}",
                    "expected": test_case.expected_output,
                    "actual": None
                }
            
            # Run the process
            process = subprocess.run(
                cmd,
                input=input_str,
                capture_output=True,
                text=True,
                timeout=test_case.timeout
            )
            
            actual_output = process.stdout.strip()
            actual_error = process.stderr.strip()
            
            # Check for expected error
            if test_case.expected_error:
                if test_case.expected_error in actual_error or process.returncode != 0:
                    return {
                        "name": test_case.name,
                        "status": "PASSED",
                        "message": "Expected error occurred",
                        "expected": test_case.expected_error,
                        "actual": actual_error
                    }
                else:
                    return {
                        "name": test_case.name,
                        "status": "FAILED",
                        "message": "Expected error did not occur",
                        "expected": test_case.expected_error,
                        "actual": actual_error or "No error"
                    }
            
            # Check normal output
            if test_case.expected_output is not None:
                expected_str = str(test_case.expected_output).strip()
                if actual_output == expected_str:
                    return {
                        "name": test_case.name,
                        "status": "PASSED",
                        "message": "Output matches expected",
                        "expected": expected_str,
                        "actual": actual_output
                    }
                else:
                    return {
                        "name": test_case.name,
                        "status": "FAILED",
                        "message": "Output does not match expected",
                        "expected": expected_str,
                        "actual": actual_output
                    }
            
            # If no expected output specified, just check if it runs without error
            if process.returncode == 0:
                return {
                    "name": test_case.name,
                    "status": "PASSED",
                    "message": "Code executed successfully",
                    "expected": "No error",
                    "actual": actual_output or "No output"
                }
            else:
                return {
                    "name": test_case.name,
                    "status": "FAILED",
                    "message": "Code execution failed",
                    "expected": "No error",
                    "actual": actual_error
                }
                
        except subprocess.TimeoutExpired:
            return {
                "name": test_case.name,
                "status": "ERROR",
                "message": f"Test timed out after {test_case.timeout} seconds",
                "expected": test_case.expected_output,
                "actual": "TIMEOUT"
            }
        except Exception as e:
            return {
                "name": test_case.name,
                "status": "ERROR",
                "message": f"Test execution error: {str(e)}",
                "expected": test_case.expected_output,
                "actual": "ERROR"
            }
    
    def print_results(self, results: Dict[str, Any]):
        """Print test results in a formatted way"""
        print(f"\n🧪 Test Results Summary:")
        print(f"{'='*50}")
        print(f"Total Tests: {results['total_tests']}")
        print(f"✅ Passed: {results['passed']}")
        print(f"❌ Failed: {results['failed']}")
        print(f"🚨 Errors: {results['errors']}")
        
        success_rate = (results['passed'] / results['total_tests']) * 100 if results['total_tests'] > 0 else 0
        print(f"📊 Success Rate: {success_rate:.1f}%")
        
        print(f"\n📋 Detailed Results:")
        print(f"{'='*50}")
        
        for result in results['test_results']:
            status_icon = "✅" if result['status'] == "PASSED" else "❌" if result['status'] == "FAILED" else "🚨"
            print(f"{status_icon} {result['name']}: {result['status']}")
            print(f"   Message: {result['message']}")
            if result['expected'] is not None:
                print(f"   Expected: {result['expected']}")
            if result['actual'] is not None:
                print(f"   Actual: {result['actual']}")
            print()

def create_simple_tests(task_description: str) -> List[TestCase]:
    """Create simple test cases based on task description"""
    tests = []
    
    # Basic execution test
    tests.append(TestCase(
        name="Basic Execution",
        input_data=None,
        expected_output=None,  # Just check it runs
        timeout=10
    ))
    
    # Add task-specific tests based on keywords
    task_lower = task_description.lower()
    
    if "fibonacci" in task_lower:
        tests.append(TestCase(
            name="Fibonacci Test",
            input_data="5\n",
            expected_output=None,  # We'll just check it runs
            timeout=5
        ))
    
    if "hello" in task_lower and "world" in task_lower:
        tests.append(TestCase(
            name="Hello World Test",
            input_data=None,
            expected_output="Hello, World!",
            timeout=5
        ))
    
    if "sort" in task_lower:
        tests.append(TestCase(
            name="Sort Test",
            input_data="3 1 4 1 5\n",
            expected_output=None,
            timeout=5
        ))
    
    return tests

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python test_runner.py <code_file> <language> [task_description]")
        sys.exit(1)
    
    code_file = sys.argv[1]
    language = sys.argv[2]
    task_description = sys.argv[3] if len(sys.argv) > 3 else ""
    
    # Create test runner
    runner = TestRunner(language)
    
    # Create simple tests
    test_cases = create_simple_tests(task_description)
    
    print(f"🧪 Running {len(test_cases)} tests for {language} code...")
    
    # Run tests
    results = runner.run_tests(code_file, test_cases)
    
    # Print results
    runner.print_results(results)
    
    # Exit with appropriate code
    if results['failed'] > 0 or results['errors'] > 0:
        sys.exit(1)
    else:
        sys.exit(0)
